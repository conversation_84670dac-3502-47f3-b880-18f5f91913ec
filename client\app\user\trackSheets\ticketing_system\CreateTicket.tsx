"use client";
import React, { useState, useEffect, useRef, useContext } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  X,
  Plus,
  User,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  ChevronRight,
  Info,
  Users,
  FileText,
  Settings,
} from "lucide-react";
import { getAllData, getCookie } from "@/lib/helpers";
import {
  ticket_routes,
  employee_routes,
  pipeline_routes,
} from "@/lib/routePath";
import { toast } from "sonner";
import { getStageColor } from "@/lib/stageColorUtils";
import { formSubmit } from "@/lib/helpers";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Progress } from "@/components/ui/progress";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { TooltipProvider } from "@/components/ui/tooltip";
import { TrackSheetContext } from "../TrackSheetContext";

interface Stage {
  id: string;
  name: string;
  description?: string;
  order: number;
}

interface Pipeline {
  id: string;
  name: string;
  stages: Stage[];
}

interface User {
  id: number;
  username: string;
  name?: string;
}

interface Creator {
  id: string;
  username: string;
  name?: string;
}

interface TicketStage {
  stageid: string;
  assignedto: string;
  due: string;
}

interface TicketForm {
  tracksheetid: string;
  pipeline_id: string;
  owner: string;
  priority: string;
  description: string;
  stages: TicketStage[];
  pipelineStages: Stage[];
}

interface SelectedRow {
  id: string;
  invoice?: string;
}

interface CreateTicketModalProps {
  isOpen: boolean;
  onClose: () => void;
  onClearSelection: () => void;
  selectedRows?: SelectedRow[];
}

const PRIORITY_OPTIONS = [
  {
    value: "High",
    label: "High",
    color: "bg-red-100 text-red-800 border-red-200",
    icon: "🔴",
  },
  {
    value: "Medium",
    label: "Medium",
    color: "bg-yellow-100 text-yellow-800 border-yellow-200",
    icon: "🟡",
  },
  {
    value: "Low",
    label: "Low",
    color: "bg-green-100 text-green-800 border-green-200",
    icon: "🟢",
  },
];

const CreateTicketModal: React.FC<CreateTicketModalProps> = ({
  isOpen,
  onClose,
  onClearSelection,
  selectedRows = [],
}) => {
  const [ticketForms, setTicketForms] = useState<TicketForm[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [pipelines, setPipelines] = useState<Pipeline[]>([]);
  const [creator, setCreator] = useState<Creator | null>(null);
  const pipelineSelectRef = useRef<HTMLButtonElement>(null);
  const dataFetched = useRef(false);
  const [arePipelinesLoading, setArePipelinesLoading] = useState(false);
  const [areUsersLoading, setAreUsersLoading] = useState(false);
  const [isBulkFill, setIsBulkFill] = useState(true);
  const [activeTicketIndex, setActiveTicketIndex] = useState(0);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});
  const [ownerAutofillError, setOwnerAutofillError] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSelectOpen, setIsSelectOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    if (isSelectOpen) {
      const timer = setTimeout(() => {
        if (inputRef.current && typeof inputRef.current.focus === "function") {
          inputRef.current.focus();
        }
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [isSelectOpen]);

  const getFormCompletion = (ticket: TicketForm): number => {
    const requiredFields = ["pipeline_id", "owner", "priority"];
    const completedFields = requiredFields.filter(
      (field) => ticket[field as keyof TicketForm]
    );
    const stageCompletion =
      ticket.stages?.length > 0
        ? ticket.stages.filter((stage) => stage.assignedto && stage.due)
            .length / ticket.stages.length
        : 0;

    return Math.round(
      ((completedFields.length / requiredFields.length) * 0.6 +
        stageCompletion * 0.4) *
        100
    );
  };

  const validateForm = (
    ticket: TicketForm,
    index: number
  ): Record<string, string> => {
    const errors: Record<string, string> = {};

    if (!ticket.pipeline_id) errors.pipeline = "Pipeline is required";
    if (!ticket.owner) errors.owner = "Owner is required";
    if (!ticket.priority) errors.priority = "Priority is required";
    return errors;
  };

  const { setCustomFieldsReloadTrigger } = useContext(TrackSheetContext);

  useEffect(() => {
    if (!isOpen) {
      dataFetched.current = false;
      setUsers([]);
      setPipelines([]);
      setCreator(null);
      setValidationErrors({});
      setActiveTicketIndex(0);
      return;
    }

    if (dataFetched.current) {
      return;
    }
    dataFetched.current = true;

    const fetchInitialData = async () => {
      try {
        const currentUserInfo = await getAllData(
          employee_routes.GETCURRENT_USER
        );
        setCreator(currentUserInfo || null);
        // Fetch CSA and set owner here
        if (currentUserInfo && currentUserInfo.id) {
          try {
            const res = await fetch(
              employee_routes.GET_CSA(currentUserInfo.id),
              {
                credentials: "include",
              }
            );
            if (!res.ok) throw new Error("Failed to fetch CSA");
            const data = await res.json();
            const csa = data.csa;
            const ownerName = csa?.name || csa?.username || "";
            if (!ownerName) throw new Error("No CSA found for this user");
            setTicketForms((prev) =>
              prev.map((form) => ({ ...form, owner: ownerName }))
            );
            setOwnerAutofillError(false);
          } catch (err) {
            setOwnerAutofillError(true);
            toast.error(
              "Could not auto-fill owner (CSA): " +
                (err?.message || "Unknown error")
            );
          }
        }
      } catch (err) {
        dataFetched.current = false;
        toast.error("Failed to load user data.");
      }
    };
    fetchInitialData();
  }, [isOpen]);

  useEffect(() => {
    if (isOpen) {
      const initialForms: TicketForm[] =
        selectedRows && selectedRows.length > 0
          ? selectedRows.map((row) => ({
              tracksheetid: row.id || "",
              pipeline_id: "",
              owner: "",
              priority: "",
              description: "",
              stages: [],
              pipelineStages: [],
            }))
          : [
              {
                tracksheetid: "",
                pipeline_id: "",
                owner: "",
                priority: "",
                description: "",
                stages: [],
                pipelineStages: [],
              },
            ];
      setTicketForms(initialForms);

      requestAnimationFrame(() => {
        setTimeout(() => {
          pipelineSelectRef.current?.focus();
        }, 30);
      });
    }
  }, [isOpen, selectedRows]);

  const handleFetchPipelines = async () => {
    if (pipelines.length > 0 || arePipelinesLoading) return;

    setArePipelinesLoading(true);
    try {
      const pipelinesData = await getAllData(pipeline_routes.GET_PIPELINE);
      setPipelines(pipelinesData.data || []);
    } catch (err) {
      toast.error("Failed to load pipelines.");
    } finally {
      setArePipelinesLoading(false);
    }
  };

  const handleFetchUsers = async () => {
    if (users.length > 0 || areUsersLoading) return;
    setAreUsersLoading(true);
    try {
      const usersData = await getAllData(employee_routes.GETALL_USERS);
      setUsers(usersData.data || []);
    } catch (err) {
      toast.error("Failed to load users.");
    } finally {
      setAreUsersLoading(false);
    }
  };

  const handleClose = () => {
    (document.activeElement as HTMLElement)?.blur();
    onClose();
    onClearSelection();
  };

  const handleFieldChange = (
    index: number,
    field: keyof TicketForm,
    value: string
  ) => {
    setTicketForms((prev) => {
      const shouldApplyToAll = isBulkFill && prev.length > 1;
      return prev.map((t, i) => {
        if (shouldApplyToAll || i === index) {
          if (field === "pipeline_id") {
            const selectedPipeline = pipelines.find((p) => p.id === value);
            const stages = selectedPipeline?.stages || [];
            return {
              ...t,
              pipeline_id: value,
              pipelineStages: stages,
              stages: stages.map((s) => ({
                stageid: s.id,
                assignedto: "",
                due: new Date().toISOString(),
              })),
            };
          }
          return { ...t, [field]: value };
        }
        return t;
      });
    });

    setValidationErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[`${field}_${index}`];
      return newErrors;
    });
  };

  const handleStageChange = (
    index: number,
    stageid: string,
    field: keyof TicketStage,
    value: string
  ) => {
    setTicketForms((prev) => {
      const shouldApplyToAll = isBulkFill && prev.length > 1;
      return prev.map((t, i) => {
        if (shouldApplyToAll || i === index) {
          const updatedStages = t.stages.map((s) =>
            s.stageid === stageid ? { ...s, [field]: value } : s
          );
          return { ...t, stages: updatedStages };
        }
        return t;
      });
    });

    setValidationErrors((prev) => {
      const newErrors = { ...prev };
      const stageIndex = ticketForms[index]?.stages?.findIndex(
        (s) => s.stageid === stageid
      );
      if (stageIndex !== -1) {
        delete newErrors[`stage_${stageIndex}_${field}`];
      }
      return newErrors;
    });
  };

  const handleAddTicketForm = () => {
    setTicketForms((prev) => [
      ...prev,
      {
        tracksheetid: "",
        description: "",
        pipeline_id: "",
        owner: "",
        priority: "",
        stages: [],
        pipelineStages: [],
      },
    ]);
  };

  const handleRemoveTicketForm = (index: number) => {
    setTicketForms((prev) => prev.filter((_, i) => i !== index));
    if (activeTicketIndex >= index && activeTicketIndex > 0) {
      setActiveTicketIndex(activeTicketIndex - 1);
    }
  };

  const handleFinalSubmit = async () => {
    const allErrors: Record<string, string> = {};
    ticketForms.forEach((ticket, index) => {
      const errors = validateForm(ticket, index);
      Object.keys(errors).forEach((key) => {
        allErrors[`${key}_${index}`] = errors[key];
      });
    });

    if (Object.keys(allErrors).length > 0) {
      setValidationErrors(allErrors);
      toast.error("Please fix the validation errors before submitting.");
      return;
    }

    const validTickets = ticketForms.filter(
      (t) => t.pipeline_id && t.owner && t.priority && t.stages.length
    );
    if (!validTickets.length) return toast.error("Fill all required fields.");

    const ticketsWithCreator = validTickets.map((ticket, index) => ({
      tracksheetid: ticket.tracksheetid,
      title: `Invoice ${
        selectedRows[index]?.invoice || ticket.tracksheetid || "N/A"
      }`,
      description: ticket.description || "",
      pipeline_id: ticket.pipeline_id,
      owner: ticket.owner,
      priority: ticket.priority,
      stages: ticket.stages.map((s) => ({
        stageid: s.stageid,
        assignedto: s.assignedto,
        due: s.due,
      })),
      createdBy: creator?.username,
    }));

    try {
      const data = await formSubmit(
        ticket_routes.CREATE_TICKET,
        "POST",
        ticketsWithCreator
      );
      if (
        data &&
        data.message &&
        data.message.toLowerCase().includes("success")
      ) {
        toast.success("Tickets created successfully.");
        setCustomFieldsReloadTrigger((prev) => prev + 1);
        handleClose();
      } else {
        toast.error((data && data.message) || "Failed to create.");
      }
    } catch {
      toast.error("Network/server error.");
    }
  };

  if (!isOpen) return null;

  return (
    <TooltipProvider>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm px-4">
        <div className="bg-white dark:bg-gray-900 rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] p-0 relative transition-transform scale-100 border border-gray-200 dark:border-gray-800 flex flex-col">
          <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-6 rounded-t-xl">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2 flex items-center gap-2 text-gray-900 dark:text-gray-100">
                  <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  Create New Ticket{ticketForms.length > 1 ? "s" : ""}
                </h2>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  {ticketForms.length > 1
                    ? `Creating ${ticketForms.length} tickets from selected items`
                    : "Fill in the details below to create a new ticket"}
                </p>
              </div>
            </div>
          </div>

          {ticketForms.length > 1 && (
            <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-1 overflow-x-auto">
                {ticketForms.map((ticket, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveTicketIndex(index)}
                    className={`flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium transition-all whitespace-nowrap ${
                      activeTicketIndex === index
                        ? "bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white shadow"
                        : "bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600"
                    }`}
                  >
                    <span>Ticket {index + 1}</span>
                    <Badge
                      variant={
                        getFormCompletion(ticket) === 100
                          ? "default"
                          : "secondary"
                      }
                      className="text-xs px-2 py-0.5"
                    >
                      {getFormCompletion(ticket)}%
                    </Badge>
                  </button>
                ))}
              </div>
            </div>
          )}

          <div className="flex-grow overflow-y-auto p-6">
            {ticketForms.length > 1 && (
              <Card className="mb-4">
                <CardContent className="p-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Settings className="w-4 h-4 text-gray-500" />
                      <div>
                        <Label
                          htmlFor="bulk-fill-switch"
                          className="text-[15px] font-medium"
                        >
                          Apply changes to all tickets
                        </Label>
                        <p className="text-xs text-gray-500 leading-tight">
                          When enabled, changes will be applied to all tickets
                          simultaneously
                        </p>
                      </div>
                    </div>
                    <Switch
                      id="bulk-fill-switch"
                      checked={isBulkFill}
                      onCheckedChange={setIsBulkFill}
                      className="scale-90"
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {ticketForms.map((ticket, index) => (
              <div
                key={index}
                className={`${
                  ticketForms.length > 1 && activeTicketIndex !== index
                    ? "hidden"
                    : ""
                }`}
              >
                <Card className="mb-6">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 dark:text-blue-400 font-semibold">
                            {index + 1}
                          </span>
                        </div>
                        <div>
                          <CardTitle className="text-lg">
                            Invoice{" "}
                            {selectedRows[index]?.invoice ||
                              ticket.tracksheetid ||
                              "N/A"}
                          </CardTitle>
                          <div className="flex items-center gap-2 mt-1">
                            <Progress
                              value={getFormCompletion(ticket)}
                              className="w-32 h-2"
                            />
                            <span className="text-sm text-gray-500">
                              {getFormCompletion(ticket)}% complete
                            </span>
                          </div>
                        </div>
                      </div>

                      {ticketForms.length > 1 && (
                        <button
                          onClick={() => handleRemoveTicketForm(index)}
                          className="text-gray-400 hover:text-red-500 transition-colors p-2"
                        >
                          <X className="w-5 h-5" />
                        </button>
                      )}
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium flex items-center gap-1">
                          <User className="w-4 h-4" />
                          Owner <span className="text-gray-500">*</span>
                        </Label>
                        <Input
                          value={ticket.owner}
                          onChange={(e) =>
                            handleFieldChange(index, "owner", e.target.value)
                          }
                          readOnly={!ownerAutofillError}
                          className="bg-gray-50 dark:bg-gray-800"
                          placeholder={
                            ownerAutofillError
                              ? "Enter owner manually"
                              : "Auto-filled from CSA"
                          }
                        />
                        {ownerAutofillError && (
                          <p className="text-red-500 text-xs mt-1">
                            Could not auto-fill Owner. Please enter manually.
                          </p>
                        )}
                        {validationErrors[`owner_${index}`] && (
                          <p className="text-red-500 text-xs flex items-center gap-1">
                            <AlertCircle className="w-3 h-3" />
                            {validationErrors[`owner_${index}`]}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium flex items-center gap-1">
                          <Settings className="w-4 h-4" />
                          Pipeline <span className="text-red-500">*</span>
                        </Label>
                        <Select
                          value={ticket.pipeline_id}
                          onValueChange={(value) =>
                            handleFieldChange(index, "pipeline_id", value)
                          }
                          onOpenChange={(open) => {
                            if (open) handleFetchPipelines();
                          }}
                        >
                          <SelectTrigger
                            ref={index === 0 ? pipelineSelectRef : null}
                            className={`${
                              validationErrors[`pipeline_${index}`]
                                ? "border-red-500 focus:ring-red-500"
                                : ""
                            }`}
                          >
                            <SelectValue placeholder="Select Pipeline" />
                          </SelectTrigger>
                          <SelectContent>
                            {arePipelinesLoading ? (
                              <div className="flex justify-center items-center p-4 text-gray-500">
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                              </div>
                            ) : (
                              pipelines.map((p) => (
                                <SelectItem key={p.id} value={p.id}>
                                  <div className="flex items-center gap-2">
                                    <span>{p.name}</span>
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {p.stages?.length || 0} stages
                                    </Badge>
                                  </div>
                                </SelectItem>
                              ))
                            )}
                          </SelectContent>
                        </Select>
                        {validationErrors[`pipeline_${index}`] && (
                          <p className="text-red-500 text-xs flex items-center gap-1">
                            <AlertCircle className="w-3 h-3" />
                            {validationErrors[`pipeline_${index}`]}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium flex items-center gap-1">
                          <AlertCircle className="w-4 h-4" />
                          Priority <span className="text-red-500">*</span>
                        </Label>
                        <Select
                          value={ticket.priority}
                          onValueChange={(value) =>
                            handleFieldChange(index, "priority", value)
                          }
                        >
                          <SelectTrigger
                            className={`${
                              validationErrors[`priority_${index}`]
                                ? "border-red-500 focus:ring-red-500"
                                : ""
                            }`}
                          >
                            <SelectValue placeholder="Select Priority" />
                          </SelectTrigger>
                          <SelectContent>
                            {PRIORITY_OPTIONS.map((opt) => (
                              <SelectItem key={opt.value} value={opt.value}>
                                <div className="flex items-center gap-2">
                                  <span>{opt.icon}</span>
                                  <Badge className={opt.color}>
                                    {opt.label}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {validationErrors[`priority_${index}`] && (
                          <p className="text-red-500 text-xs flex items-center gap-1">
                            <AlertCircle className="w-3 h-3" />
                            {validationErrors[`priority_${index}`]}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium flex items-center gap-1">
                          <FileText className="w-4 h-4" />
                          Description
                        </Label>
                        <Textarea
                          value={ticket.description}
                          onChange={(e) =>
                            handleFieldChange(
                              index,
                              "description",
                              e.target.value
                            )
                          }
                          placeholder="Enter ticket description..."
                          className="h-10 resize-none"
                        />
                      </div>
                    </div>

                    {ticket.pipelineStages?.length > 0 && (
                      <div className="space-y-4">
                        <div className="flex items-center gap-2">
                          <h3 className="text-lg font-semibold">
                            {pipelines.find((p) => p.id === ticket.pipeline_id)
                              ?.name || "Pipeline"}
                          </h3>
                          <Badge variant="outline" className="text-sm">
                            {ticket.pipelineStages.length} stages
                          </Badge>
                        </div>
                        <div className="flex flex-row gap-4 overflow-x-auto flex-nowrap py-4">
                          {ticket.pipelineStages.map((stage, sidx) => (
                            <React.Fragment key={stage.id}>
                              <Card className="min-w-[220px] max-w-[260px] flex-shrink-0 shadow-sm border border-gray-200 dark:border-gray-700">
                                <CardContent className="p-4 flex flex-col gap-2">
                                  <div className="flex items-center gap-2 mb-1">
                                    <Badge className={getStageColor(stage.id)}>
                                      {stage.name}
                                    </Badge>
                                    <span className="text-xs text-gray-400">
                                      #{sidx + 1}
                                    </span>
                                  </div>
                                  {stage.description && (
                                    <div className="text-xs text-gray-500 mb-1">
                                      {stage.description}
                                    </div>
                                  )}
                                  <div className="space-y-2">
                                    <div>
                                      <Label className="block text-[11px] text-gray-500 mb-0.5">
                                        Assign To
                                      </Label>
                                      <Select
                                        value={
                                          ticket.stages[sidx]?.assignedto || ""
                                        }
                                        onValueChange={(value) =>
                                          handleStageChange(
                                            index,
                                            stage.id,
                                            "assignedto",
                                            value
                                          )
                                        }
                                        onOpenChange={(open) => {
                                          setIsSelectOpen(open);
                                          if (open) {
                                            handleFetchUsers();
                                            setSearchTerm("");
                                          }
                                        }}
                                      >
                                        <SelectTrigger className="w-full h-8 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]">
                                          <SelectValue placeholder="Select User" />
                                        </SelectTrigger>
                                        <SelectContent
                                          onPointerDown={(e) =>
                                            e.stopPropagation()
                                          }
                                          className="bg-white dark:bg-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto mt-1"
                                        >
                                          <div className="sticky top-0 z-10 bg-white dark:bg-gray-700 p-2">
                                            <input
                                              ref={inputRef}
                                              type="text"
                                              placeholder="Search user..."
                                              value={searchTerm}
                                              onChange={(e) =>
                                                setSearchTerm(e.target.value)
                                              }
                                              className="w-full h-8 px-2 rounded border border-gray-300 dark:border-gray-600 text-sm bg-white dark:bg-gray-800 text-black dark:text-white"
                                            />
                                          </div>
                                          {areUsersLoading ? (
                                            <div className="flex justify-center items-center p-2 text-gray-500">
                                              Loading...
                                            </div>
                                          ) : (
                                            users
                                              .filter((u) =>
                                                u.username
                                                  .toLowerCase()
                                                  .includes(
                                                    searchTerm.toLowerCase()
                                                  )
                                              )
                                              .map((u) => (
                                                <SelectItem
                                                  key={u.id}
                                                  value={u.id.toString()}
                                                  className="px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded cursor-pointer transition text-[13px]"
                                                >
                                                  {u.username}
                                                </SelectItem>
                                              ))
                                          )}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                    <div>
                                      <Label className="block text-[11px] text-gray-500 mb-0.5">
                                        Due Date
                                      </Label>
                                      <Input
                                        type="date"
                                        className="w-full h-8 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]"
                                        value={
                                          ticket.stages[sidx]?.due?.split(
                                            "T"
                                          )[0] || ""
                                        }
                                        onChange={(e) =>
                                          handleStageChange(
                                            index,
                                            stage.id,
                                            "due",
                                            new Date(
                                              e.target.value
                                            ).toISOString()
                                          )
                                        }
                                      />
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                              {sidx < ticket.pipelineStages.length - 1 && (
                                <div className="flex items-center mx-1">
                                  <svg
                                    width="20"
                                    height="20"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      d="M8 4l8 8-8 8"
                                      stroke="#9ca3af"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                  </svg>
                                </div>
                              )}
                            </React.Fragment>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            ))}

            {(!selectedRows || selectedRows.length === 0) && (
              <div className="flex justify-center">
                <Button
                  onClick={handleAddTicketForm}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Add Another Ticket
                </Button>
              </div>
            )}
          </div>

          <div className="bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 p-6 rounded-b-xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <Info className="w-4 h-4" />
                  <span>Fields marked with * are required</span>
                </div>
                {ticketForms.length > 1 && (
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <Clock className="w-4 h-4" />
                    <span>{ticketForms.length} tickets to create</span>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-3">
                <Button variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
                <Button
                  onClick={handleFinalSubmit}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6"
                  disabled={ticketForms.some(
                    (ticket) =>
                      !ticket.pipeline_id || !ticket.owner || !ticket.priority
                  )}
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Create Ticket{ticketForms.length > 1 ? "s" : ""}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default CreateTicketModal;
