"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/ticketing_system/CreateTicket.tsx":
/*!****************************************************************!*\
  !*** ./app/user/trackSheets/ticketing_system/CreateTicket.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_stageColorUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/stageColorUtils */ \"(app-pages-browser)/./lib/stageColorUtils.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PRIORITY_OPTIONS = [\n    {\n        value: \"High\",\n        label: \"High\",\n        color: \"bg-red-100 text-red-800 border-red-200\",\n        icon: \"\\uD83D\\uDD34\"\n    },\n    {\n        value: \"Medium\",\n        label: \"Medium\",\n        color: \"bg-yellow-100 text-yellow-800 border-yellow-200\",\n        icon: \"\\uD83D\\uDFE1\"\n    },\n    {\n        value: \"Low\",\n        label: \"Low\",\n        color: \"bg-green-100 text-green-800 border-green-200\",\n        icon: \"\\uD83D\\uDFE2\"\n    }\n];\nconst CreateTicketModal = (param)=>{\n    let { isOpen, onClose, onClearSelection, selectedRows = [] } = param;\n    _s();\n    const [ticketForms, setTicketForms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pipelines, setPipelines] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [creator, setCreator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const pipelineSelectRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dataFetched = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [arePipelinesLoading, setArePipelinesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [areUsersLoading, setAreUsersLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBulkFill, setIsBulkFill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTicketIndex, setActiveTicketIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [ownerAutofillError, setOwnerAutofillError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSelectOpen, setIsSelectOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isSelectOpen) {\n            const timer = setTimeout(()=>{\n                if (inputRef.current && typeof inputRef.current.focus === \"function\") {\n                    inputRef.current.focus();\n                }\n            }, 50);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        isSelectOpen\n    ]);\n    const getFormCompletion = (ticket)=>{\n        var _ticket_stages;\n        const requiredFields = [\n            \"pipeline_id\",\n            \"owner\",\n            \"priority\"\n        ];\n        const completedFields = requiredFields.filter((field)=>ticket[field]);\n        const stageCompletion = ((_ticket_stages = ticket.stages) === null || _ticket_stages === void 0 ? void 0 : _ticket_stages.length) > 0 ? ticket.stages.filter((stage)=>stage.assignedto && stage.due).length / ticket.stages.length : 0;\n        return Math.round((completedFields.length / requiredFields.length * 0.6 + stageCompletion * 0.4) * 100);\n    };\n    const validateForm = (ticket, index)=>{\n        const errors = {};\n        if (!ticket.pipeline_id) errors.pipeline = \"Pipeline is required\";\n        if (!ticket.owner) errors.owner = \"Owner is required\";\n        if (!ticket.priority) errors.priority = \"Priority is required\";\n        return errors;\n    };\n    const { setCustomFieldsReloadTrigger } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__.TrackSheetContext);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) {\n            dataFetched.current = false;\n            setUsers([]);\n            setPipelines([]);\n            setCreator(null);\n            setValidationErrors({});\n            setActiveTicketIndex(0);\n            return;\n        }\n        if (dataFetched.current) {\n            return;\n        }\n        dataFetched.current = true;\n        const fetchInitialData = async ()=>{\n            try {\n                const currentUserInfo = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GETCURRENT_USER);\n                setCreator(currentUserInfo || null);\n                // Fetch CSA and set owner here\n                if (currentUserInfo && currentUserInfo.id) {\n                    try {\n                        const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GET_CSA(currentUserInfo.id), {\n                            credentials: \"include\"\n                        });\n                        if (!res.ok) throw new Error(\"Failed to fetch CSA\");\n                        const data = await res.json();\n                        const csa = data.csa;\n                        const ownerName = (csa === null || csa === void 0 ? void 0 : csa.name) || (csa === null || csa === void 0 ? void 0 : csa.username) || \"\";\n                        if (!ownerName) throw new Error(\"No CSA found for this user\");\n                        setTicketForms((prev)=>prev.map((form)=>({\n                                    ...form,\n                                    owner: ownerName\n                                })));\n                        setOwnerAutofillError(false);\n                    } catch (err) {\n                        setOwnerAutofillError(true);\n                        sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Could not auto-fill owner (CSA): \" + ((err === null || err === void 0 ? void 0 : err.message) || \"Unknown error\"));\n                    }\n                }\n            } catch (err) {\n                dataFetched.current = false;\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load user data.\");\n            }\n        };\n        fetchInitialData();\n    }, [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            const initialForms = selectedRows && selectedRows.length > 0 ? selectedRows.map((row)=>({\n                    tracksheetid: row.id || \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    description: \"\",\n                    stages: [],\n                    pipelineStages: []\n                })) : [\n                {\n                    tracksheetid: \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    description: \"\",\n                    stages: [],\n                    pipelineStages: []\n                }\n            ];\n            setTicketForms(initialForms);\n            requestAnimationFrame(()=>{\n                setTimeout(()=>{\n                    var _pipelineSelectRef_current;\n                    (_pipelineSelectRef_current = pipelineSelectRef.current) === null || _pipelineSelectRef_current === void 0 ? void 0 : _pipelineSelectRef_current.focus();\n                }, 30);\n            });\n        }\n    }, [\n        isOpen,\n        selectedRows\n    ]);\n    const handleFetchPipelines = async ()=>{\n        if (pipelines.length > 0 || arePipelinesLoading) return;\n        setArePipelinesLoading(true);\n        try {\n            const pipelinesData = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.pipeline_routes.GET_PIPELINE);\n            setPipelines(pipelinesData.data || []);\n        } catch (err) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load pipelines.\");\n        } finally{\n            setArePipelinesLoading(false);\n        }\n    };\n    const handleFetchUsers = async ()=>{\n        if (users.length > 0 || areUsersLoading) return;\n        setAreUsersLoading(true);\n        try {\n            const usersData = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GETALL_USERS);\n            setUsers(usersData.data || []);\n        } catch (err) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load users.\");\n        } finally{\n            setAreUsersLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        var _document_activeElement;\n        (_document_activeElement = document.activeElement) === null || _document_activeElement === void 0 ? void 0 : _document_activeElement.blur();\n        onClose();\n        onClearSelection();\n    };\n    const handleFieldChange = (index, field, value)=>{\n        setTicketForms((prev)=>{\n            const shouldApplyToAll = isBulkFill && prev.length > 1;\n            return prev.map((t, i)=>{\n                if (shouldApplyToAll || i === index) {\n                    if (field === \"pipeline_id\") {\n                        const selectedPipeline = pipelines.find((p)=>p.id === value);\n                        const stages = (selectedPipeline === null || selectedPipeline === void 0 ? void 0 : selectedPipeline.stages) || [];\n                        return {\n                            ...t,\n                            pipeline_id: value,\n                            pipelineStages: stages,\n                            stages: stages.map((s)=>({\n                                    stageid: s.id,\n                                    assignedto: \"\",\n                                    due: new Date().toISOString()\n                                }))\n                        };\n                    }\n                    return {\n                        ...t,\n                        [field]: value\n                    };\n                }\n                return t;\n            });\n        });\n        setValidationErrors((prev)=>{\n            const newErrors = {\n                ...prev\n            };\n            delete newErrors[\"\".concat(field, \"_\").concat(index)];\n            return newErrors;\n        });\n    };\n    const handleStageChange = (index, stageid, field, value)=>{\n        setTicketForms((prev)=>{\n            const shouldApplyToAll = isBulkFill && prev.length > 1;\n            return prev.map((t, i)=>{\n                if (shouldApplyToAll || i === index) {\n                    const updatedStages = t.stages.map((s)=>s.stageid === stageid ? {\n                            ...s,\n                            [field]: value\n                        } : s);\n                    return {\n                        ...t,\n                        stages: updatedStages\n                    };\n                }\n                return t;\n            });\n        });\n        setValidationErrors((prev)=>{\n            var _ticketForms_index_stages, _ticketForms_index;\n            const newErrors = {\n                ...prev\n            };\n            const stageIndex = (_ticketForms_index = ticketForms[index]) === null || _ticketForms_index === void 0 ? void 0 : (_ticketForms_index_stages = _ticketForms_index.stages) === null || _ticketForms_index_stages === void 0 ? void 0 : _ticketForms_index_stages.findIndex((s)=>s.stageid === stageid);\n            if (stageIndex !== -1) {\n                delete newErrors[\"stage_\".concat(stageIndex, \"_\").concat(field)];\n            }\n            return newErrors;\n        });\n    };\n    const handleAddTicketForm = ()=>{\n        setTicketForms((prev)=>[\n                ...prev,\n                {\n                    tracksheetid: \"\",\n                    description: \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    stages: [],\n                    pipelineStages: []\n                }\n            ]);\n    };\n    const handleRemoveTicketForm = (index)=>{\n        setTicketForms((prev)=>prev.filter((_, i)=>i !== index));\n        if (activeTicketIndex >= index && activeTicketIndex > 0) {\n            setActiveTicketIndex(activeTicketIndex - 1);\n        }\n    };\n    const handleFinalSubmit = async ()=>{\n        const allErrors = {};\n        ticketForms.forEach((ticket, index)=>{\n            const errors = validateForm(ticket, index);\n            Object.keys(errors).forEach((key)=>{\n                allErrors[\"\".concat(key, \"_\").concat(index)] = errors[key];\n            });\n        });\n        if (Object.keys(allErrors).length > 0) {\n            setValidationErrors(allErrors);\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Please fix the validation errors before submitting.\");\n            return;\n        }\n        const validTickets = ticketForms.filter((t)=>t.pipeline_id && t.owner && t.priority && t.stages.length);\n        if (!validTickets.length) return sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Fill all required fields.\");\n        const ticketsWithCreator = validTickets.map((ticket, index)=>{\n            var _selectedRows_index;\n            return {\n                tracksheetid: ticket.tracksheetid,\n                title: \"Invoice \".concat(((_selectedRows_index = selectedRows[index]) === null || _selectedRows_index === void 0 ? void 0 : _selectedRows_index.invoice) || ticket.tracksheetid || \"N/A\"),\n                description: ticket.description || \"\",\n                pipeline_id: ticket.pipeline_id,\n                owner: ticket.owner,\n                priority: ticket.priority,\n                stages: ticket.stages.map((s)=>({\n                        stageid: s.stageid,\n                        assignedto: s.assignedto,\n                        due: s.due\n                    })),\n                createdBy: creator === null || creator === void 0 ? void 0 : creator.username\n            };\n        });\n        try {\n            const data = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.ticket_routes.CREATE_TICKET, \"POST\", ticketsWithCreator);\n            if (data && data.message && data.message.toLowerCase().includes(\"success\")) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Tickets created successfully.\");\n                setCustomFieldsReloadTrigger((prev)=>prev + 1);\n                handleClose();\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(data && data.message || \"Failed to create.\");\n            }\n        } catch (e) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Network/server error.\");\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-900 rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] p-0 relative transition-transform scale-100 border border-gray-200 dark:border-gray-800 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-6 rounded-t-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-2 flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Create New Ticket\",\n                                            ticketForms.length > 1 ? \"s\" : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                        children: ticketForms.length > 1 ? \"Creating \".concat(ticketForms.length, \" tickets from selected items\") : \"Fill in the details below to create a new ticket\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 11\n                    }, undefined),\n                    ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-800 px-4 py-2 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1 overflow-x-auto\",\n                            children: ticketForms.map((ticket, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTicketIndex(index),\n                                    className: \"flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium transition-all whitespace-nowrap \".concat(activeTicketIndex === index ? \"bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white shadow\" : \"bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Ticket \",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: getFormCompletion(ticket) === 100 ? \"default\" : \"secondary\",\n                                            className: \"text-xs px-2 py-0.5\",\n                                            children: [\n                                                getFormCompletion(ticket),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-grow overflow-y-auto p-6\",\n                        children: [\n                            ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                    className: \"p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                htmlFor: \"bulk-fill-switch\",\n                                                                className: \"text-[15px] font-medium\",\n                                                                children: \"Apply changes to all tickets\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 leading-tight\",\n                                                                children: \"When enabled, changes will be applied to all tickets simultaneously\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                id: \"bulk-fill-switch\",\n                                                checked: isBulkFill,\n                                                onCheckedChange: setIsBulkFill,\n                                                className: \"scale-90\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 15\n                            }, undefined),\n                            ticketForms.map((ticket, index)=>{\n                                var _selectedRows_index, _ticket_pipelineStages, _pipelines_find;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(ticketForms.length > 1 && activeTicketIndex !== index ? \"hidden\" : \"\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardHeader, {\n                                                className: \"pb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 dark:text-blue-400 font-semibold\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 553,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardTitle, {\n                                                                            className: \"text-lg\",\n                                                                            children: [\n                                                                                \"Invoice\",\n                                                                                \" \",\n                                                                                ((_selectedRows_index = selectedRows[index]) === null || _selectedRows_index === void 0 ? void 0 : _selectedRows_index.invoice) || ticket.tracksheetid || \"N/A\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_12__.Progress, {\n                                                                                    value: getFormCompletion(ticket),\n                                                                                    className: \"w-32 h-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 565,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        getFormCompletion(ticket),\n                                                                                        \"% complete\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 569,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleRemoveTicketForm(index),\n                                                            className: \"text-gray-400 hover:text-red-500 transition-colors p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 591,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Owner \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        value: ticket.owner,\n                                                                        onChange: (e)=>handleFieldChange(index, \"owner\", e.target.value),\n                                                                        readOnly: !ownerAutofillError,\n                                                                        className: \"bg-gray-50 dark:bg-gray-800\",\n                                                                        placeholder: ownerAutofillError ? \"Enter owner manually\" : \"Auto-filled from CSA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    ownerAutofillError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs mt-1\",\n                                                                        children: \"Could not auto-fill Owner. Please enter manually.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 608,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    validationErrors[\"owner_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 614,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"owner_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 613,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 622,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Pipeline \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 623,\n                                                                                columnNumber: 36\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                        value: ticket.pipeline_id,\n                                                                        onValueChange: (value)=>handleFieldChange(index, \"pipeline_id\", value),\n                                                                        onOpenChange: (open)=>{\n                                                                            if (open) handleFetchPipelines();\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                ref: index === 0 ? pipelineSelectRef : null,\n                                                                                className: \"\".concat(validationErrors[\"pipeline_\".concat(index)] ? \"border-red-500 focus:ring-red-500\" : \"\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                    placeholder: \"Select Pipeline\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 642,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                children: arePipelinesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-center items-center p-4 text-gray-500\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 647,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 646,\n                                                                                    columnNumber: 31\n                                                                                }, undefined) : pipelines.map((p)=>{\n                                                                                    var _p_stages;\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                        value: p.id,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: p.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 653,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    variant: \"outline\",\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: [\n                                                                                                        ((_p_stages = p.stages) === null || _p_stages === void 0 ? void 0 : _p_stages.length) || 0,\n                                                                                                        \" stages\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 654,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 652,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, p.id, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 651,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined);\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 644,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    validationErrors[\"pipeline_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 668,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"pipeline_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 667,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 676,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Priority \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 677,\n                                                                                columnNumber: 36\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                        value: ticket.priority,\n                                                                        onValueChange: (value)=>handleFieldChange(index, \"priority\", value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                className: \"\".concat(validationErrors[\"priority_\".concat(index)] ? \"border-red-500 focus:ring-red-500\" : \"\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                    placeholder: \"Select Priority\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 692,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 685,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                children: PRIORITY_OPTIONS.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                        value: opt.value,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: opt.icon\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 698,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    className: opt.color,\n                                                                                                    children: opt.label\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 699,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 697,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, opt.value, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 696,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 694,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    validationErrors[\"priority_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 709,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"priority_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 708,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 674,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 717,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Description\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 716,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__.Textarea, {\n                                                                        value: ticket.description,\n                                                                        onChange: (e)=>handleFieldChange(index, \"description\", e.target.value),\n                                                                        placeholder: \"Enter ticket description...\",\n                                                                        className: \"h-10 resize-none\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    ((_ticket_pipelineStages = ticket.pipelineStages) === null || _ticket_pipelineStages === void 0 ? void 0 : _ticket_pipelineStages.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold\",\n                                                                        children: ((_pipelines_find = pipelines.find((p)=>p.id === ticket.pipeline_id)) === null || _pipelines_find === void 0 ? void 0 : _pipelines_find.name) || \"Pipeline\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 738,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-sm\",\n                                                                        children: [\n                                                                            ticket.pipelineStages.length,\n                                                                            \" stages\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 742,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-row gap-4 overflow-x-auto flex-nowrap py-4\",\n                                                                children: ticket.pipelineStages.map((stage, sidx)=>{\n                                                                    var _ticket_stages_sidx, _ticket_stages_sidx_due, _ticket_stages_sidx1;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                                                                className: \"min-w-[220px] max-w-[260px] flex-shrink-0 shadow-sm border border-gray-200 dark:border-gray-700\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                                                                    className: \"p-4 flex flex-col gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 mb-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    className: (0,_lib_stageColorUtils__WEBPACK_IMPORTED_MODULE_9__.getStageColor)(stage.id),\n                                                                                                    children: stage.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 752,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-xs text-gray-400\",\n                                                                                                    children: [\n                                                                                                        \"#\",\n                                                                                                        sidx + 1\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 755,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 751,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        stage.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-gray-500 mb-1\",\n                                                                                            children: stage.description\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 760,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                                                            className: \"block text-[11px] text-gray-500 mb-0.5\",\n                                                                                                            children: \"Assign To\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 766,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                                                            value: ((_ticket_stages_sidx = ticket.stages[sidx]) === null || _ticket_stages_sidx === void 0 ? void 0 : _ticket_stages_sidx.assignedto) || \"\",\n                                                                                                            onValueChange: (value)=>handleStageChange(index, stage.id, \"assignedto\", value),\n                                                                                                            onOpenChange: (open)=>{\n                                                                                                                setIsSelectOpen(open);\n                                                                                                                if (open) {\n                                                                                                                    handleFetchUsers();\n                                                                                                                    setSearchTerm(\"\");\n                                                                                                                }\n                                                                                                            },\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                                                    className: \"w-full h-8 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]\",\n                                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                                                        placeholder: \"Select User\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                        lineNumber: 790,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined)\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                    lineNumber: 789,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                                                    onPointerDown: (e)=>e.stopPropagation(),\n                                                                                                                    className: \"bg-white dark:bg-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto mt-1\",\n                                                                                                                    children: [\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                            className: \"sticky top-0 z-10 bg-white dark:bg-gray-700 p-2\",\n                                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                                                ref: inputRef,\n                                                                                                                                type: \"text\",\n                                                                                                                                placeholder: \"Search user...\",\n                                                                                                                                value: searchTerm,\n                                                                                                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                                                                                                className: \"w-full h-8 px-2 rounded border border-gray-300 dark:border-gray-600 text-sm bg-white dark:bg-gray-800 text-black dark:text-white\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                                lineNumber: 799,\n                                                                                                                                columnNumber: 45\n                                                                                                                            }, undefined)\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                            lineNumber: 798,\n                                                                                                                            columnNumber: 43\n                                                                                                                        }, undefined),\n                                                                                                                        areUsersLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                            className: \"flex justify-center items-center p-2 text-gray-500\",\n                                                                                                                            children: \"Loading...\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                            lineNumber: 811,\n                                                                                                                            columnNumber: 45\n                                                                                                                        }, undefined) : users.filter((u)=>u.username.toLowerCase().includes(searchTerm.toLowerCase())).map((u)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                                                                value: u.id.toString(),\n                                                                                                                                className: \"px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded cursor-pointer transition text-[13px]\",\n                                                                                                                                children: u.username\n                                                                                                                            }, u.id, false, {\n                                                                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                                lineNumber: 824,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, undefined))\n                                                                                                                    ]\n                                                                                                                }, void 0, true, {\n                                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                    lineNumber: 792,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 769,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 765,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                                                            className: \"block text-[11px] text-gray-500 mb-0.5\",\n                                                                                                            children: \"Due Date\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 837,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                            type: \"date\",\n                                                                                                            className: \"w-full h-8 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]\",\n                                                                                                            value: ((_ticket_stages_sidx1 = ticket.stages[sidx]) === null || _ticket_stages_sidx1 === void 0 ? void 0 : (_ticket_stages_sidx_due = _ticket_stages_sidx1.due) === null || _ticket_stages_sidx_due === void 0 ? void 0 : _ticket_stages_sidx_due.split(\"T\")[0]) || \"\",\n                                                                                                            onChange: (e)=>handleStageChange(index, stage.id, \"due\", new Date(e.target.value).toISOString())\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 840,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 836,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 764,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 750,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 749,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            sidx < ticket.pipelineStages.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center mx-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M8 4l8 8-8 8\",\n                                                                                        stroke: \"#9ca3af\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 871,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 865,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 864,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, stage.id, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 29\n                                                                    }, undefined);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 746,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, undefined);\n                            }),\n                            (!selectedRows || selectedRows.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleAddTicketForm,\n                                    variant: \"outline\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 898,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Add Another Ticket\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 893,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 892,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 p-6 rounded-b-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 909,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Fields marked with * are required\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 910,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 908,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 914,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        ticketForms.length,\n                                                        \" tickets to create\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 913,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 907,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: handleClose,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleFinalSubmit,\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6\",\n                                            disabled: ticketForms.some((ticket)=>!ticket.pipeline_id || !ticket.owner || !ticket.priority),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Create Ticket\",\n                                                ticketForms.length > 1 ? \"s\" : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 906,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 905,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                lineNumber: 461,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n            lineNumber: 460,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n        lineNumber: 459,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTicketModal, \"NOIs4dAshuT34/HdsFBB7jnp29I=\");\n_c = CreateTicketModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTicketModal);\nvar _c;\n$RefreshReg$(_c, \"CreateTicketModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/ticketing_system/CreateTicket.tsx\n"));

/***/ })

});