"use client"

import { useState } from "react"
import { Trash2, UserPlus, Download, Loader2 } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { InvoiceFile } from "../types"
import { useBulkDeleteInvoiceFiles, useBulkUpdateInvoiceFiles } from "../hooks/useInvoiceFiles"
import { Alert, AlertDescription } from "@/app/_component/Alert"
import { useUsers } from "../hooks/useUser"

interface BulkActionsProps {
  selectedFiles: InvoiceFile[]
  onActionComplete: () => void
}

export function BulkActions({ selectedFiles, onActionComplete }: BulkActionsProps) {
  const [showAssignDialog, setShowAssignDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedUserId, setSelectedUserId] = useState<string>("")
  const [isProcessing, setIsProcessing] = useState(false)

  const { data: users } = useUsers()
  const { bulkUpdateInvoiceFiles } = useBulkUpdateInvoiceFiles()
  const { bulkDeleteInvoiceFiles } = useBulkDeleteInvoiceFiles()

  const handleBulkAssign = async () => {
    if (!selectedUserId) return

    setIsProcessing(true)
    try {
      const fileIds = selectedFiles.map((file) => file.id)
      await bulkUpdateInvoiceFiles(fileIds, {
        assignedTo: Number.parseInt(selectedUserId),
      })

      toast.success(`${selectedFiles.length} files assigned successfully`)
      setShowAssignDialog(false)
      setSelectedUserId("")
      onActionComplete()
    } catch (error) {
      toast.error("Failed to assign files")
      console.error("Error in bulk assign:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleBulkDelete = async () => {
    setIsProcessing(true)
    try {
      const fileIds = selectedFiles.map((file) => file.id)
      await bulkDeleteInvoiceFiles(fileIds)

      toast.success(`${selectedFiles.length} files deleted successfully`)
      setShowDeleteDialog(false)
      onActionComplete()
    } catch (error) {
      toast.error("Failed to delete files")
      console.error("Error in bulk delete:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleExport = () => {
    // Export selected files functionality
    const csvContent = [
      "Date,File Name,Pages,Assigned To,Status,Created At",
      ...selectedFiles.map((file) =>
        [
          file.date,
          file.fileName,
          file.noOfPages,
          file.assignedToUser ? `${file.assignedToUser.firstName} ${file.assignedToUser.lastName}` : "Unassigned",
          file.deletedAt ? "Deleted" : "Active",
          file.createdAt,
        ].join(","),
      ),
    ].join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `invoice-files-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)

    toast.success("Files exported successfully")
  }

  if (selectedFiles.length === 0) return null

  return (
    <>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium">
                {selectedFiles.length} file{selectedFiles.length !== 1 ? "s" : ""} selected
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={() => setShowAssignDialog(true)}>
                <UserPlus className="mr-2 h-4 w-4" />
                Assign
              </Button>

              <Button variant="outline" size="sm" onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>

              {/* <Button variant="destructive" size="sm" onClick={() => setShowDeleteDialog(true)}>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button> */}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Assign Dialog */}
      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Files</DialogTitle>
            <DialogDescription>
              Assign {selectedFiles.length} selected file{selectedFiles.length !== 1 ? "s" : ""} to a user.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Select User</label>
              <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                <SelectTrigger className="mt-2">
                  <SelectValue placeholder="Choose a user to assign files to" />
                </SelectTrigger>
                <SelectContent>
                  {users?.map((user) => (
                    <SelectItem key={user.id} value={user.id.toString()}>
                      {user.firstName} {user.lastName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Alert>
              <AlertDescription>
                This will assign all {selectedFiles.length} selected files to the chosen user.
              </AlertDescription>
            </Alert>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAssignDialog(false)} disabled={isProcessing}>
              Cancel
            </Button>
            <Button onClick={handleBulkAssign} disabled={!selectedUserId || isProcessing}>
              {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Assign Files
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Files</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedFiles.length} selected file
              {selectedFiles.length !== 1 ? "s" : ""}?
            </DialogDescription>
          </DialogHeader>

          <Alert>
            <AlertDescription>
              This will soft delete all selected files. They can be restored later if needed.
            </AlertDescription>
          </Alert>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)} disabled={isProcessing}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleBulkDelete} disabled={isProcessing}>
              {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete Files
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
