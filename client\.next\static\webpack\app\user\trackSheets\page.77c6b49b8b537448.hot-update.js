"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/ticketing_system/CreateTicket.tsx":
/*!****************************************************************!*\
  !*** ./app/user/trackSheets/ticketing_system/CreateTicket.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_stageColorUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/stageColorUtils */ \"(app-pages-browser)/./lib/stageColorUtils.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PRIORITY_OPTIONS = [\n    {\n        value: \"High\",\n        label: \"High\",\n        color: \"bg-red-100 text-red-800 border-red-200\",\n        icon: \"\\uD83D\\uDD34\"\n    },\n    {\n        value: \"Medium\",\n        label: \"Medium\",\n        color: \"bg-yellow-100 text-yellow-800 border-yellow-200\",\n        icon: \"\\uD83D\\uDFE1\"\n    },\n    {\n        value: \"Low\",\n        label: \"Low\",\n        color: \"bg-green-100 text-green-800 border-green-200\",\n        icon: \"\\uD83D\\uDFE2\"\n    }\n];\nconst CreateTicketModal = (param)=>{\n    let { isOpen, onClose, onClearSelection, selectedRows = [] } = param;\n    _s();\n    const [ticketForms, setTicketForms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pipelines, setPipelines] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [creator, setCreator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const pipelineSelectRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dataFetched = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [arePipelinesLoading, setArePipelinesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [areUsersLoading, setAreUsersLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBulkFill, setIsBulkFill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTicketIndex, setActiveTicketIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [ownerAutofillError, setOwnerAutofillError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSelectOpen, setIsSelectOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isSelectOpen) {\n            const timer = setTimeout(()=>{\n                if (inputRef.current && typeof inputRef.current.focus === \"function\") {\n                    inputRef.current.focus();\n                    // Ensure the input stays focused\n                    inputRef.current.addEventListener(\"blur\", (e)=>{\n                        const relatedTarget = e.relatedTarget;\n                        if (relatedTarget && !relatedTarget.closest('[role=\"option\"]')) {\n                            setTimeout(()=>{\n                                if (inputRef.current && isSelectOpen) {\n                                    inputRef.current.focus();\n                                }\n                            }, 0);\n                        }\n                    }, {\n                        once: true\n                    });\n                }\n            }, 50);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        isSelectOpen\n    ]);\n    const getFormCompletion = (ticket)=>{\n        var _ticket_stages;\n        const requiredFields = [\n            \"pipeline_id\",\n            \"owner\",\n            \"priority\"\n        ];\n        const completedFields = requiredFields.filter((field)=>ticket[field]);\n        const stageCompletion = ((_ticket_stages = ticket.stages) === null || _ticket_stages === void 0 ? void 0 : _ticket_stages.length) > 0 ? ticket.stages.filter((stage)=>stage.assignedto && stage.due).length / ticket.stages.length : 0;\n        return Math.round((completedFields.length / requiredFields.length * 0.6 + stageCompletion * 0.4) * 100);\n    };\n    const validateForm = (ticket)=>{\n        const errors = {};\n        if (!ticket.pipeline_id) errors.pipeline = \"Pipeline is required\";\n        if (!ticket.owner) errors.owner = \"Owner is required\";\n        if (!ticket.priority) errors.priority = \"Priority is required\";\n        return errors;\n    };\n    const { setCustomFieldsReloadTrigger } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__.TrackSheetContext);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) {\n            dataFetched.current = false;\n            setUsers([]);\n            setPipelines([]);\n            setCreator(null);\n            setValidationErrors({});\n            setActiveTicketIndex(0);\n            return;\n        }\n        if (dataFetched.current) {\n            return;\n        }\n        dataFetched.current = true;\n        const fetchInitialData = async ()=>{\n            try {\n                const currentUserInfo = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GETCURRENT_USER);\n                setCreator(currentUserInfo || null);\n                // Fetch CSA and set owner here\n                if (currentUserInfo && currentUserInfo.id) {\n                    try {\n                        const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GET_CSA(currentUserInfo.id), {\n                            credentials: \"include\"\n                        });\n                        if (!res.ok) throw new Error(\"Failed to fetch CSA\");\n                        const data = await res.json();\n                        const csa = data.csa;\n                        const ownerName = (csa === null || csa === void 0 ? void 0 : csa.name) || (csa === null || csa === void 0 ? void 0 : csa.username) || \"\";\n                        if (!ownerName) throw new Error(\"No CSA found for this user\");\n                        setTicketForms((prev)=>prev.map((form)=>({\n                                    ...form,\n                                    owner: ownerName\n                                })));\n                        setOwnerAutofillError(false);\n                    } catch (err) {\n                        setOwnerAutofillError(true);\n                        sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Could not auto-fill owner (CSA): \" + ((err === null || err === void 0 ? void 0 : err.message) || \"Unknown error\"));\n                    }\n                }\n            } catch (err) {\n                dataFetched.current = false;\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load user data.\");\n            }\n        };\n        fetchInitialData();\n    }, [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            const initialForms = selectedRows && selectedRows.length > 0 ? selectedRows.map((row)=>({\n                    tracksheetid: row.id || \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    description: \"\",\n                    stages: [],\n                    pipelineStages: []\n                })) : [\n                {\n                    tracksheetid: \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    description: \"\",\n                    stages: [],\n                    pipelineStages: []\n                }\n            ];\n            setTicketForms(initialForms);\n            requestAnimationFrame(()=>{\n                setTimeout(()=>{\n                    var _pipelineSelectRef_current;\n                    (_pipelineSelectRef_current = pipelineSelectRef.current) === null || _pipelineSelectRef_current === void 0 ? void 0 : _pipelineSelectRef_current.focus();\n                }, 30);\n            });\n        }\n    }, [\n        isOpen,\n        selectedRows\n    ]);\n    const handleFetchPipelines = async ()=>{\n        if (pipelines.length > 0 || arePipelinesLoading) return;\n        setArePipelinesLoading(true);\n        try {\n            const pipelinesData = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.pipeline_routes.GET_PIPELINE);\n            setPipelines(pipelinesData.data || []);\n        } catch (err) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load pipelines.\");\n        } finally{\n            setArePipelinesLoading(false);\n        }\n    };\n    const handleFetchUsers = async ()=>{\n        if (users.length > 0 || areUsersLoading) return;\n        setAreUsersLoading(true);\n        try {\n            const usersData = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GETALL_USERS);\n            setUsers(usersData.data || []);\n        } catch (err) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load users.\");\n        } finally{\n            setAreUsersLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        var _document_activeElement;\n        (_document_activeElement = document.activeElement) === null || _document_activeElement === void 0 ? void 0 : _document_activeElement.blur();\n        onClose();\n        onClearSelection();\n    };\n    const handleFieldChange = (index, field, value)=>{\n        setTicketForms((prev)=>{\n            const shouldApplyToAll = isBulkFill && prev.length > 1;\n            return prev.map((t, i)=>{\n                if (shouldApplyToAll || i === index) {\n                    if (field === \"pipeline_id\") {\n                        const selectedPipeline = pipelines.find((p)=>p.id === value);\n                        const stages = (selectedPipeline === null || selectedPipeline === void 0 ? void 0 : selectedPipeline.stages) || [];\n                        return {\n                            ...t,\n                            pipeline_id: value,\n                            pipelineStages: stages,\n                            stages: stages.map((s)=>({\n                                    stageid: s.id,\n                                    assignedto: \"\",\n                                    due: new Date().toISOString()\n                                }))\n                        };\n                    }\n                    return {\n                        ...t,\n                        [field]: value\n                    };\n                }\n                return t;\n            });\n        });\n        setValidationErrors((prev)=>{\n            const newErrors = {\n                ...prev\n            };\n            delete newErrors[\"\".concat(field, \"_\").concat(index)];\n            return newErrors;\n        });\n    };\n    const handleStageChange = (index, stageid, field, value)=>{\n        setTicketForms((prev)=>{\n            const shouldApplyToAll = isBulkFill && prev.length > 1;\n            return prev.map((t, i)=>{\n                if (shouldApplyToAll || i === index) {\n                    const updatedStages = t.stages.map((s)=>s.stageid === stageid ? {\n                            ...s,\n                            [field]: value\n                        } : s);\n                    return {\n                        ...t,\n                        stages: updatedStages\n                    };\n                }\n                return t;\n            });\n        });\n        setValidationErrors((prev)=>{\n            var _ticketForms_index_stages, _ticketForms_index;\n            const newErrors = {\n                ...prev\n            };\n            const stageIndex = (_ticketForms_index = ticketForms[index]) === null || _ticketForms_index === void 0 ? void 0 : (_ticketForms_index_stages = _ticketForms_index.stages) === null || _ticketForms_index_stages === void 0 ? void 0 : _ticketForms_index_stages.findIndex((s)=>s.stageid === stageid);\n            if (stageIndex !== -1) {\n                delete newErrors[\"stage_\".concat(stageIndex, \"_\").concat(field)];\n            }\n            return newErrors;\n        });\n    };\n    const handleAddTicketForm = ()=>{\n        setTicketForms((prev)=>[\n                ...prev,\n                {\n                    tracksheetid: \"\",\n                    description: \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    stages: [],\n                    pipelineStages: []\n                }\n            ]);\n    };\n    const handleRemoveTicketForm = (index)=>{\n        setTicketForms((prev)=>prev.filter((_, i)=>i !== index));\n        if (activeTicketIndex >= index && activeTicketIndex > 0) {\n            setActiveTicketIndex(activeTicketIndex - 1);\n        }\n    };\n    const handleFinalSubmit = async ()=>{\n        const allErrors = {};\n        ticketForms.forEach((ticket, index)=>{\n            const errors = validateForm(ticket);\n            Object.keys(errors).forEach((key)=>{\n                allErrors[\"\".concat(key, \"_\").concat(index)] = errors[key];\n            });\n        });\n        if (Object.keys(allErrors).length > 0) {\n            setValidationErrors(allErrors);\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Please fix the validation errors before submitting.\");\n            return;\n        }\n        const validTickets = ticketForms.filter((t)=>t.pipeline_id && t.owner && t.priority && t.stages.length);\n        if (!validTickets.length) return sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Fill all required fields.\");\n        const ticketsWithCreator = validTickets.map((ticket, index)=>{\n            var _selectedRows_index;\n            return {\n                tracksheetid: ticket.tracksheetid,\n                title: \"Invoice \".concat(((_selectedRows_index = selectedRows[index]) === null || _selectedRows_index === void 0 ? void 0 : _selectedRows_index.invoice) || ticket.tracksheetid || \"N/A\"),\n                description: ticket.description || \"\",\n                pipeline_id: ticket.pipeline_id,\n                owner: ticket.owner,\n                priority: ticket.priority,\n                stages: ticket.stages.map((s)=>({\n                        stageid: s.stageid,\n                        assignedto: s.assignedto,\n                        due: s.due\n                    })),\n                createdBy: creator === null || creator === void 0 ? void 0 : creator.username\n            };\n        });\n        try {\n            const data = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.ticket_routes.CREATE_TICKET, \"POST\", ticketsWithCreator);\n            if (data && data.message && data.message.toLowerCase().includes(\"success\")) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Tickets created successfully.\");\n                setCustomFieldsReloadTrigger((prev)=>prev + 1);\n                handleClose();\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(data && data.message || \"Failed to create.\");\n            }\n        } catch (e) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Network/server error.\");\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-900 rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] p-0 relative transition-transform scale-100 border border-gray-200 dark:border-gray-800 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-6 rounded-t-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-2 flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Create New Ticket\",\n                                            ticketForms.length > 1 ? \"s\" : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                        children: ticketForms.length > 1 ? \"Creating \".concat(ticketForms.length, \" tickets from selected items\") : \"Fill in the details below to create a new ticket\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 11\n                    }, undefined),\n                    ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-800 px-4 py-2 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1 overflow-x-auto\",\n                            children: ticketForms.map((ticket, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTicketIndex(index),\n                                    className: \"flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium transition-all whitespace-nowrap \".concat(activeTicketIndex === index ? \"bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white shadow\" : \"bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Ticket \",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: getFormCompletion(ticket) === 100 ? \"default\" : \"secondary\",\n                                            className: \"text-xs px-2 py-0.5\",\n                                            children: [\n                                                getFormCompletion(ticket),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-grow overflow-y-auto p-6\",\n                        children: [\n                            ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                    className: \"p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                htmlFor: \"bulk-fill-switch\",\n                                                                className: \"text-[15px] font-medium\",\n                                                                children: \"Apply changes to all tickets\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 leading-tight\",\n                                                                children: \"When enabled, changes will be applied to all tickets simultaneously\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                id: \"bulk-fill-switch\",\n                                                checked: isBulkFill,\n                                                onCheckedChange: setIsBulkFill,\n                                                className: \"scale-90\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 520,\n                                columnNumber: 15\n                            }, undefined),\n                            ticketForms.map((ticket, index)=>{\n                                var _selectedRows_index, _ticket_pipelineStages, _pipelines_find;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(ticketForms.length > 1 && activeTicketIndex !== index ? \"hidden\" : \"\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardHeader, {\n                                                className: \"pb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 dark:text-blue-400 font-semibold\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardTitle, {\n                                                                            className: \"text-lg\",\n                                                                            children: [\n                                                                                \"Invoice\",\n                                                                                \" \",\n                                                                                ((_selectedRows_index = selectedRows[index]) === null || _selectedRows_index === void 0 ? void 0 : _selectedRows_index.invoice) || ticket.tracksheetid || \"N/A\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_12__.Progress, {\n                                                                                    value: getFormCompletion(ticket),\n                                                                                    className: \"w-32 h-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 575,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        getFormCompletion(ticket),\n                                                                                        \"% complete\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 579,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleRemoveTicketForm(index),\n                                                            className: \"text-gray-400 hover:text-red-500 transition-colors p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 601,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Owner \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 602,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        value: ticket.owner,\n                                                                        onChange: (e)=>handleFieldChange(index, \"owner\", e.target.value),\n                                                                        readOnly: !ownerAutofillError,\n                                                                        className: \"bg-gray-50 dark:bg-gray-800\",\n                                                                        placeholder: ownerAutofillError ? \"Enter owner manually\" : \"Auto-filled from CSA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 604,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    ownerAutofillError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs mt-1\",\n                                                                        children: \"Could not auto-fill Owner. Please enter manually.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    validationErrors[\"owner_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 624,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"owner_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 632,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Pipeline \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 633,\n                                                                                columnNumber: 36\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                        value: ticket.pipeline_id,\n                                                                        onValueChange: (value)=>handleFieldChange(index, \"pipeline_id\", value),\n                                                                        onOpenChange: (open)=>{\n                                                                            if (open) handleFetchPipelines();\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                ref: index === 0 ? pipelineSelectRef : null,\n                                                                                className: \"\".concat(validationErrors[\"pipeline_\".concat(index)] ? \"border-red-500 focus:ring-red-500\" : \"\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                    placeholder: \"Select Pipeline\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 652,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 644,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                children: arePipelinesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-center items-center p-4 text-gray-500\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 657,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 656,\n                                                                                    columnNumber: 31\n                                                                                }, undefined) : pipelines.map((p)=>{\n                                                                                    var _p_stages;\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                        value: p.id,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: p.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 663,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    variant: \"outline\",\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: [\n                                                                                                        ((_p_stages = p.stages) === null || _p_stages === void 0 ? void 0 : _p_stages.length) || 0,\n                                                                                                        \" stages\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 664,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 662,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, p.id, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 661,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined);\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 654,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    validationErrors[\"pipeline_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 678,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"pipeline_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 677,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 686,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Priority \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 687,\n                                                                                columnNumber: 36\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 685,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                        value: ticket.priority,\n                                                                        onValueChange: (value)=>handleFieldChange(index, \"priority\", value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                className: \"\".concat(validationErrors[\"priority_\".concat(index)] ? \"border-red-500 focus:ring-red-500\" : \"\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                    placeholder: \"Select Priority\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 702,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 695,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                children: PRIORITY_OPTIONS.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                        value: opt.value,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: opt.icon\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 708,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    className: opt.color,\n                                                                                                    children: opt.label\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 709,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 707,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, opt.value, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 706,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 704,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 689,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    validationErrors[\"priority_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 719,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"priority_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 718,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 727,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Description\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__.Textarea, {\n                                                                        value: ticket.description,\n                                                                        onChange: (e)=>handleFieldChange(index, \"description\", e.target.value),\n                                                                        placeholder: \"Enter ticket description...\",\n                                                                        className: \"h-10 resize-none\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 730,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 725,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    ((_ticket_pipelineStages = ticket.pipelineStages) === null || _ticket_pipelineStages === void 0 ? void 0 : _ticket_pipelineStages.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold\",\n                                                                        children: ((_pipelines_find = pipelines.find((p)=>p.id === ticket.pipeline_id)) === null || _pipelines_find === void 0 ? void 0 : _pipelines_find.name) || \"Pipeline\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-sm\",\n                                                                        children: [\n                                                                            ticket.pipelineStages.length,\n                                                                            \" stages\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 752,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-row gap-4 overflow-x-auto flex-nowrap py-4\",\n                                                                children: ticket.pipelineStages.map((stage, sidx)=>{\n                                                                    var _ticket_stages_sidx, _ticket_stages_sidx_due, _ticket_stages_sidx1;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                                                                className: \"min-w-[220px] max-w-[260px] flex-shrink-0 shadow-sm border border-gray-200 dark:border-gray-700\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                                                                    className: \"p-4 flex flex-col gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 mb-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    className: (0,_lib_stageColorUtils__WEBPACK_IMPORTED_MODULE_9__.getStageColor)(stage.id),\n                                                                                                    children: stage.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 762,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-xs text-gray-400\",\n                                                                                                    children: [\n                                                                                                        \"#\",\n                                                                                                        sidx + 1\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 765,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 761,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        stage.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-gray-500 mb-1\",\n                                                                                            children: stage.description\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 770,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                                                            className: \"block text-[11px] text-gray-500 mb-0.5\",\n                                                                                                            children: \"Assign To\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 776,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                                                            value: ((_ticket_stages_sidx = ticket.stages[sidx]) === null || _ticket_stages_sidx === void 0 ? void 0 : _ticket_stages_sidx.assignedto) || \"\",\n                                                                                                            onValueChange: (value)=>handleStageChange(index, stage.id, \"assignedto\", value),\n                                                                                                            onOpenChange: (open)=>{\n                                                                                                                setIsSelectOpen(open);\n                                                                                                                if (open) {\n                                                                                                                    handleFetchUsers();\n                                                                                                                    setSearchTerm(\"\");\n                                                                                                                }\n                                                                                                            },\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                                                    className: \"w-full h-8 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]\",\n                                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                                                        placeholder: \"Select User\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                        lineNumber: 800,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined)\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                    lineNumber: 799,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                                                    onPointerDown: (e)=>e.stopPropagation(),\n                                                                                                                    onCloseAutoFocus: (e)=>{\n                                                                                                                        e.preventDefault();\n                                                                                                                    },\n                                                                                                                    className: \"bg-white dark:bg-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto mt-1\",\n                                                                                                                    children: [\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                            className: \"sticky top-0 z-10 bg-white dark:bg-gray-700 p-2\",\n                                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                                                ref: inputRef,\n                                                                                                                                type: \"text\",\n                                                                                                                                placeholder: \"Search user...\",\n                                                                                                                                value: searchTerm,\n                                                                                                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                                                                                                onKeyDown: (e)=>{\n                                                                                                                                    if (e.key === \"Enter\") {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        e.stopPropagation();\n                                                                                                                                        // Find the first filtered user and select it\n                                                                                                                                        const filteredUsers = users.filter((u)=>u.username.toLowerCase().includes(searchTerm.toLowerCase()));\n                                                                                                                                        if (filteredUsers.length > 0) {\n                                                                                                                                            handleStageChange(index, stage.id, \"assignedto\", filteredUsers[0].id.toString());\n                                                                                                                                            setSearchTerm(\"\");\n                                                                                                                                            setIsSelectOpen(false);\n                                                                                                                                        }\n                                                                                                                                    }\n                                                                                                                                    if (e.key === \"Escape\") {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        e.stopPropagation();\n                                                                                                                                        setIsSelectOpen(false);\n                                                                                                                                    }\n                                                                                                                                },\n                                                                                                                                onFocus: (e)=>{\n                                                                                                                                    e.stopPropagation();\n                                                                                                                                },\n                                                                                                                                onBlur: (e)=>{\n                                                                                                                                    // Prevent blur when clicking on SelectItem\n                                                                                                                                    const relatedTarget = e.relatedTarget;\n                                                                                                                                    if (relatedTarget && relatedTarget.closest('[role=\"option\"]')) {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        setTimeout(()=>{\n                                                                                                                                            if (inputRef.current) {\n                                                                                                                                                inputRef.current.focus();\n                                                                                                                                            }\n                                                                                                                                        }, 0);\n                                                                                                                                    }\n                                                                                                                                },\n                                                                                                                                className: \"w-full h-8 px-2 rounded border border-gray-300 dark:border-gray-600 text-sm bg-white dark:bg-gray-800 text-black dark:text-white\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                                lineNumber: 812,\n                                                                                                                                columnNumber: 45\n                                                                                                                            }, undefined)\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                            lineNumber: 811,\n                                                                                                                            columnNumber: 43\n                                                                                                                        }, undefined),\n                                                                                                                        areUsersLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                            className: \"flex justify-center items-center p-2 text-gray-500\",\n                                                                                                                            children: \"Loading...\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                            lineNumber: 866,\n                                                                                                                            columnNumber: 45\n                                                                                                                        }, undefined) : users.filter((u)=>u.username.toLowerCase().includes(searchTerm.toLowerCase())).map((u)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                                                                value: u.id.toString(),\n                                                                                                                                className: \"px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded cursor-pointer transition text-[13px]\",\n                                                                                                                                onMouseDown: (e)=>{\n                                                                                                                                    e.preventDefault();\n                                                                                                                                },\n                                                                                                                                onClick: ()=>{\n                                                                                                                                    handleStageChange(index, stage.id, \"assignedto\", u.id.toString());\n                                                                                                                                    setSearchTerm(\"\");\n                                                                                                                                    setIsSelectOpen(false);\n                                                                                                                                },\n                                                                                                                                children: u.username\n                                                                                                                            }, u.id, false, {\n                                                                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                                lineNumber: 879,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, undefined))\n                                                                                                                    ]\n                                                                                                                }, void 0, true, {\n                                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                    lineNumber: 802,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 779,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 775,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                                                            className: \"block text-[11px] text-gray-500 mb-0.5\",\n                                                                                                            children: \"Due Date\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 905,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                            type: \"date\",\n                                                                                                            className: \"w-full h-8 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]\",\n                                                                                                            value: ((_ticket_stages_sidx1 = ticket.stages[sidx]) === null || _ticket_stages_sidx1 === void 0 ? void 0 : (_ticket_stages_sidx_due = _ticket_stages_sidx1.due) === null || _ticket_stages_sidx_due === void 0 ? void 0 : _ticket_stages_sidx_due.split(\"T\")[0]) || \"\",\n                                                                                                            onChange: (e)=>handleStageChange(index, stage.id, \"due\", new Date(e.target.value).toISOString())\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 908,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 904,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 774,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 760,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 759,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            sidx < ticket.pipelineStages.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center mx-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M8 4l8 8-8 8\",\n                                                                                        stroke: \"#9ca3af\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 939,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 933,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 932,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, stage.id, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 758,\n                                                                        columnNumber: 29\n                                                                    }, undefined);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, undefined);\n                            }),\n                            (!selectedRows || selectedRows.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleAddTicketForm,\n                                    variant: \"outline\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Add Another Ticket\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 960,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 p-6 rounded-b-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 977,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Fields marked with * are required\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 978,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 976,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 982,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        ticketForms.length,\n                                                        \" tickets to create\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 981,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 975,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: handleClose,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 989,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleFinalSubmit,\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6\",\n                                            disabled: ticketForms.some((ticket)=>!ticket.pipeline_id || !ticket.owner || !ticket.priority),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 1000,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Create Ticket\",\n                                                ticketForms.length > 1 ? \"s\" : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 992,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 988,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 974,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 973,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                lineNumber: 471,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n            lineNumber: 470,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n        lineNumber: 469,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTicketModal, \"NOIs4dAshuT34/HdsFBB7jnp29I=\");\n_c = CreateTicketModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTicketModal);\nvar _c;\n$RefreshReg$(_c, \"CreateTicketModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/ticketing_system/CreateTicket.tsx\n"));

/***/ })

});