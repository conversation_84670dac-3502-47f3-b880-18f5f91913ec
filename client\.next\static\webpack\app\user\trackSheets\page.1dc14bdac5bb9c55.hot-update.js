"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/ticketing_system/CreateTicket.tsx":
/*!****************************************************************!*\
  !*** ./app/user/trackSheets/ticketing_system/CreateTicket.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_stageColorUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/stageColorUtils */ \"(app-pages-browser)/./lib/stageColorUtils.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PRIORITY_OPTIONS = [\n    {\n        value: \"High\",\n        label: \"High\",\n        color: \"bg-red-100 text-red-800 border-red-200\",\n        icon: \"\\uD83D\\uDD34\"\n    },\n    {\n        value: \"Medium\",\n        label: \"Medium\",\n        color: \"bg-yellow-100 text-yellow-800 border-yellow-200\",\n        icon: \"\\uD83D\\uDFE1\"\n    },\n    {\n        value: \"Low\",\n        label: \"Low\",\n        color: \"bg-green-100 text-green-800 border-green-200\",\n        icon: \"\\uD83D\\uDFE2\"\n    }\n];\nconst CreateTicketModal = (param)=>{\n    let { isOpen, onClose, onClearSelection, selectedRows = [] } = param;\n    _s();\n    const [ticketForms, setTicketForms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pipelines, setPipelines] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [creator, setCreator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const pipelineSelectRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dataFetched = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [arePipelinesLoading, setArePipelinesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [areUsersLoading, setAreUsersLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBulkFill, setIsBulkFill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTicketIndex, setActiveTicketIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [ownerAutofillError, setOwnerAutofillError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSelectOpen, setIsSelectOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUserIndex, setSelectedUserIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isSelectOpen) {\n            const timer = setTimeout(()=>{\n                if (inputRef.current && typeof inputRef.current.focus === \"function\") {\n                    inputRef.current.focus();\n                    // Ensure the input stays focused\n                    inputRef.current.addEventListener(\"blur\", (e)=>{\n                        const relatedTarget = e.relatedTarget;\n                        if (relatedTarget && !relatedTarget.closest('[role=\"option\"]')) {\n                            setTimeout(()=>{\n                                if (inputRef.current && isSelectOpen) {\n                                    inputRef.current.focus();\n                                }\n                            }, 0);\n                        }\n                    }, {\n                        once: true\n                    });\n                }\n            }, 50);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        isSelectOpen\n    ]);\n    const getFormCompletion = (ticket)=>{\n        var _ticket_stages;\n        const requiredFields = [\n            \"pipeline_id\",\n            \"owner\",\n            \"priority\"\n        ];\n        const completedFields = requiredFields.filter((field)=>ticket[field]);\n        const stageCompletion = ((_ticket_stages = ticket.stages) === null || _ticket_stages === void 0 ? void 0 : _ticket_stages.length) > 0 ? ticket.stages.filter((stage)=>stage.assignedto && stage.due).length / ticket.stages.length : 0;\n        return Math.round((completedFields.length / requiredFields.length * 0.6 + stageCompletion * 0.4) * 100);\n    };\n    const validateForm = (ticket)=>{\n        const errors = {};\n        if (!ticket.pipeline_id) errors.pipeline = \"Pipeline is required\";\n        if (!ticket.owner) errors.owner = \"Owner is required\";\n        if (!ticket.priority) errors.priority = \"Priority is required\";\n        return errors;\n    };\n    const { setCustomFieldsReloadTrigger } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__.TrackSheetContext);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) {\n            dataFetched.current = false;\n            setUsers([]);\n            setPipelines([]);\n            setCreator(null);\n            setValidationErrors({});\n            setActiveTicketIndex(0);\n            return;\n        }\n        if (dataFetched.current) {\n            return;\n        }\n        dataFetched.current = true;\n        const fetchInitialData = async ()=>{\n            try {\n                const currentUserInfo = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GETCURRENT_USER);\n                setCreator(currentUserInfo || null);\n                // Fetch CSA and set owner here\n                if (currentUserInfo && currentUserInfo.id) {\n                    try {\n                        const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GET_CSA(currentUserInfo.id), {\n                            credentials: \"include\"\n                        });\n                        if (!res.ok) throw new Error(\"Failed to fetch CSA\");\n                        const data = await res.json();\n                        const csa = data.csa;\n                        const ownerName = (csa === null || csa === void 0 ? void 0 : csa.name) || (csa === null || csa === void 0 ? void 0 : csa.username) || \"\";\n                        if (!ownerName) throw new Error(\"No CSA found for this user\");\n                        setTicketForms((prev)=>prev.map((form)=>({\n                                    ...form,\n                                    owner: ownerName\n                                })));\n                        setOwnerAutofillError(false);\n                    } catch (err) {\n                        setOwnerAutofillError(true);\n                        sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Could not auto-fill owner (CSA): \" + ((err === null || err === void 0 ? void 0 : err.message) || \"Unknown error\"));\n                    }\n                }\n            } catch (err) {\n                dataFetched.current = false;\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load user data.\");\n            }\n        };\n        fetchInitialData();\n    }, [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            const initialForms = selectedRows && selectedRows.length > 0 ? selectedRows.map((row)=>({\n                    tracksheetid: row.id || \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    description: \"\",\n                    stages: [],\n                    pipelineStages: []\n                })) : [\n                {\n                    tracksheetid: \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    description: \"\",\n                    stages: [],\n                    pipelineStages: []\n                }\n            ];\n            setTicketForms(initialForms);\n            requestAnimationFrame(()=>{\n                setTimeout(()=>{\n                    var _pipelineSelectRef_current;\n                    (_pipelineSelectRef_current = pipelineSelectRef.current) === null || _pipelineSelectRef_current === void 0 ? void 0 : _pipelineSelectRef_current.focus();\n                }, 30);\n            });\n        }\n    }, [\n        isOpen,\n        selectedRows\n    ]);\n    const handleFetchPipelines = async ()=>{\n        if (pipelines.length > 0 || arePipelinesLoading) return;\n        setArePipelinesLoading(true);\n        try {\n            const pipelinesData = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.pipeline_routes.GET_PIPELINE);\n            setPipelines(pipelinesData.data || []);\n        } catch (err) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load pipelines.\");\n        } finally{\n            setArePipelinesLoading(false);\n        }\n    };\n    const handleFetchUsers = async ()=>{\n        if (users.length > 0 || areUsersLoading) return;\n        setAreUsersLoading(true);\n        try {\n            const usersData = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GETALL_USERS);\n            setUsers(usersData.data || []);\n        } catch (err) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load users.\");\n        } finally{\n            setAreUsersLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        var _document_activeElement;\n        (_document_activeElement = document.activeElement) === null || _document_activeElement === void 0 ? void 0 : _document_activeElement.blur();\n        onClose();\n        onClearSelection();\n    };\n    const handleFieldChange = (index, field, value)=>{\n        setTicketForms((prev)=>{\n            const shouldApplyToAll = isBulkFill && prev.length > 1;\n            return prev.map((t, i)=>{\n                if (shouldApplyToAll || i === index) {\n                    if (field === \"pipeline_id\") {\n                        const selectedPipeline = pipelines.find((p)=>p.id === value);\n                        const stages = (selectedPipeline === null || selectedPipeline === void 0 ? void 0 : selectedPipeline.stages) || [];\n                        return {\n                            ...t,\n                            pipeline_id: value,\n                            pipelineStages: stages,\n                            stages: stages.map((s)=>({\n                                    stageid: s.id,\n                                    assignedto: \"\",\n                                    due: new Date().toISOString()\n                                }))\n                        };\n                    }\n                    return {\n                        ...t,\n                        [field]: value\n                    };\n                }\n                return t;\n            });\n        });\n        setValidationErrors((prev)=>{\n            const newErrors = {\n                ...prev\n            };\n            delete newErrors[\"\".concat(field, \"_\").concat(index)];\n            return newErrors;\n        });\n    };\n    const handleStageChange = (index, stageid, field, value)=>{\n        setTicketForms((prev)=>{\n            const shouldApplyToAll = isBulkFill && prev.length > 1;\n            return prev.map((t, i)=>{\n                if (shouldApplyToAll || i === index) {\n                    const updatedStages = t.stages.map((s)=>s.stageid === stageid ? {\n                            ...s,\n                            [field]: value\n                        } : s);\n                    return {\n                        ...t,\n                        stages: updatedStages\n                    };\n                }\n                return t;\n            });\n        });\n        setValidationErrors((prev)=>{\n            var _ticketForms_index_stages, _ticketForms_index;\n            const newErrors = {\n                ...prev\n            };\n            const stageIndex = (_ticketForms_index = ticketForms[index]) === null || _ticketForms_index === void 0 ? void 0 : (_ticketForms_index_stages = _ticketForms_index.stages) === null || _ticketForms_index_stages === void 0 ? void 0 : _ticketForms_index_stages.findIndex((s)=>s.stageid === stageid);\n            if (stageIndex !== -1) {\n                delete newErrors[\"stage_\".concat(stageIndex, \"_\").concat(field)];\n            }\n            return newErrors;\n        });\n    };\n    const handleAddTicketForm = ()=>{\n        setTicketForms((prev)=>[\n                ...prev,\n                {\n                    tracksheetid: \"\",\n                    description: \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    stages: [],\n                    pipelineStages: []\n                }\n            ]);\n    };\n    const handleRemoveTicketForm = (index)=>{\n        setTicketForms((prev)=>prev.filter((_, i)=>i !== index));\n        if (activeTicketIndex >= index && activeTicketIndex > 0) {\n            setActiveTicketIndex(activeTicketIndex - 1);\n        }\n    };\n    const handleFinalSubmit = async ()=>{\n        const allErrors = {};\n        ticketForms.forEach((ticket, index)=>{\n            const errors = validateForm(ticket);\n            Object.keys(errors).forEach((key)=>{\n                allErrors[\"\".concat(key, \"_\").concat(index)] = errors[key];\n            });\n        });\n        if (Object.keys(allErrors).length > 0) {\n            setValidationErrors(allErrors);\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Please fix the validation errors before submitting.\");\n            return;\n        }\n        const validTickets = ticketForms.filter((t)=>t.pipeline_id && t.owner && t.priority && t.stages.length);\n        if (!validTickets.length) return sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Fill all required fields.\");\n        const ticketsWithCreator = validTickets.map((ticket, index)=>{\n            var _selectedRows_index;\n            return {\n                tracksheetid: ticket.tracksheetid,\n                title: \"Invoice \".concat(((_selectedRows_index = selectedRows[index]) === null || _selectedRows_index === void 0 ? void 0 : _selectedRows_index.invoice) || ticket.tracksheetid || \"N/A\"),\n                description: ticket.description || \"\",\n                pipeline_id: ticket.pipeline_id,\n                owner: ticket.owner,\n                priority: ticket.priority,\n                stages: ticket.stages.map((s)=>({\n                        stageid: s.stageid,\n                        assignedto: s.assignedto,\n                        due: s.due\n                    })),\n                createdBy: creator === null || creator === void 0 ? void 0 : creator.username\n            };\n        });\n        try {\n            const data = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.ticket_routes.CREATE_TICKET, \"POST\", ticketsWithCreator);\n            if (data && data.message && data.message.toLowerCase().includes(\"success\")) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Tickets created successfully.\");\n                setCustomFieldsReloadTrigger((prev)=>prev + 1);\n                handleClose();\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(data && data.message || \"Failed to create.\");\n            }\n        } catch (e) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Network/server error.\");\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-900 rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] p-0 relative transition-transform scale-100 border border-gray-200 dark:border-gray-800 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-6 rounded-t-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-2 flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Create New Ticket\",\n                                            ticketForms.length > 1 ? \"s\" : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                        children: ticketForms.length > 1 ? \"Creating \".concat(ticketForms.length, \" tickets from selected items\") : \"Fill in the details below to create a new ticket\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 11\n                    }, undefined),\n                    ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-800 px-4 py-2 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1 overflow-x-auto\",\n                            children: ticketForms.map((ticket, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTicketIndex(index),\n                                    className: \"flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium transition-all whitespace-nowrap \".concat(activeTicketIndex === index ? \"bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white shadow\" : \"bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Ticket \",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: getFormCompletion(ticket) === 100 ? \"default\" : \"secondary\",\n                                            className: \"text-xs px-2 py-0.5\",\n                                            children: [\n                                                getFormCompletion(ticket),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-grow overflow-y-auto p-6\",\n                        children: [\n                            ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                    className: \"p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                htmlFor: \"bulk-fill-switch\",\n                                                                className: \"text-[15px] font-medium\",\n                                                                children: \"Apply changes to all tickets\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 leading-tight\",\n                                                                children: \"When enabled, changes will be applied to all tickets simultaneously\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                id: \"bulk-fill-switch\",\n                                                checked: isBulkFill,\n                                                onCheckedChange: setIsBulkFill,\n                                                className: \"scale-90\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 15\n                            }, undefined),\n                            ticketForms.map((ticket, index)=>{\n                                var _selectedRows_index, _ticket_pipelineStages, _pipelines_find;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(ticketForms.length > 1 && activeTicketIndex !== index ? \"hidden\" : \"\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardHeader, {\n                                                className: \"pb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 dark:text-blue-400 font-semibold\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardTitle, {\n                                                                            className: \"text-lg\",\n                                                                            children: [\n                                                                                \"Invoice\",\n                                                                                \" \",\n                                                                                ((_selectedRows_index = selectedRows[index]) === null || _selectedRows_index === void 0 ? void 0 : _selectedRows_index.invoice) || ticket.tracksheetid || \"N/A\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_12__.Progress, {\n                                                                                    value: getFormCompletion(ticket),\n                                                                                    className: \"w-32 h-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 576,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        getFormCompletion(ticket),\n                                                                                        \"% complete\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 580,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleRemoveTicketForm(index),\n                                                            className: \"text-gray-400 hover:text-red-500 transition-colors p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 602,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Owner \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 603,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        value: ticket.owner,\n                                                                        onChange: (e)=>handleFieldChange(index, \"owner\", e.target.value),\n                                                                        readOnly: !ownerAutofillError,\n                                                                        className: \"bg-gray-50 dark:bg-gray-800\",\n                                                                        placeholder: ownerAutofillError ? \"Enter owner manually\" : \"Auto-filled from CSA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    ownerAutofillError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs mt-1\",\n                                                                        children: \"Could not auto-fill Owner. Please enter manually.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 619,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    validationErrors[\"owner_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 625,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"owner_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 633,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Pipeline \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 36\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                        value: ticket.pipeline_id,\n                                                                        onValueChange: (value)=>handleFieldChange(index, \"pipeline_id\", value),\n                                                                        onOpenChange: (open)=>{\n                                                                            if (open) handleFetchPipelines();\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                ref: index === 0 ? pipelineSelectRef : null,\n                                                                                className: \"\".concat(validationErrors[\"pipeline_\".concat(index)] ? \"border-red-500 focus:ring-red-500\" : \"\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                    placeholder: \"Select Pipeline\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 653,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 645,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                children: arePipelinesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-center items-center p-4 text-gray-500\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 658,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 657,\n                                                                                    columnNumber: 31\n                                                                                }, undefined) : pipelines.map((p)=>{\n                                                                                    var _p_stages;\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                        value: p.id,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: p.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 664,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    variant: \"outline\",\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: [\n                                                                                                        ((_p_stages = p.stages) === null || _p_stages === void 0 ? void 0 : _p_stages.length) || 0,\n                                                                                                        \" stages\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 665,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 663,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, p.id, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 662,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined);\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 655,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 636,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    validationErrors[\"pipeline_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 679,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"pipeline_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 678,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 687,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Priority \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 688,\n                                                                                columnNumber: 36\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 686,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                        value: ticket.priority,\n                                                                        onValueChange: (value)=>handleFieldChange(index, \"priority\", value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                className: \"\".concat(validationErrors[\"priority_\".concat(index)] ? \"border-red-500 focus:ring-red-500\" : \"\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                    placeholder: \"Select Priority\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 703,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 696,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                children: PRIORITY_OPTIONS.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                        value: opt.value,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: opt.icon\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 709,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    className: opt.color,\n                                                                                                    children: opt.label\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 710,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 708,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, opt.value, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 707,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 705,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    validationErrors[\"priority_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 720,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"priority_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 728,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Description\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 727,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__.Textarea, {\n                                                                        value: ticket.description,\n                                                                        onChange: (e)=>handleFieldChange(index, \"description\", e.target.value),\n                                                                        placeholder: \"Enter ticket description...\",\n                                                                        className: \"h-10 resize-none\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 731,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    ((_ticket_pipelineStages = ticket.pipelineStages) === null || _ticket_pipelineStages === void 0 ? void 0 : _ticket_pipelineStages.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold\",\n                                                                        children: ((_pipelines_find = pipelines.find((p)=>p.id === ticket.pipeline_id)) === null || _pipelines_find === void 0 ? void 0 : _pipelines_find.name) || \"Pipeline\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-sm\",\n                                                                        children: [\n                                                                            ticket.pipelineStages.length,\n                                                                            \" stages\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-row gap-4 overflow-x-auto flex-nowrap py-4\",\n                                                                children: ticket.pipelineStages.map((stage, sidx)=>{\n                                                                    var _ticket_stages_sidx, _ticket_stages_sidx_due, _ticket_stages_sidx1;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                                                                className: \"min-w-[220px] max-w-[260px] flex-shrink-0 shadow-sm border border-gray-200 dark:border-gray-700\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                                                                    className: \"p-4 flex flex-col gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 mb-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    className: (0,_lib_stageColorUtils__WEBPACK_IMPORTED_MODULE_9__.getStageColor)(stage.id),\n                                                                                                    children: stage.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 763,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-xs text-gray-400\",\n                                                                                                    children: [\n                                                                                                        \"#\",\n                                                                                                        sidx + 1\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 766,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 762,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        stage.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-gray-500 mb-1\",\n                                                                                            children: stage.description\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 771,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                                                            className: \"block text-[11px] text-gray-500 mb-0.5\",\n                                                                                                            children: \"Assign To\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 777,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                                                            value: ((_ticket_stages_sidx = ticket.stages[sidx]) === null || _ticket_stages_sidx === void 0 ? void 0 : _ticket_stages_sidx.assignedto) || \"\",\n                                                                                                            onValueChange: (value)=>handleStageChange(index, stage.id, \"assignedto\", value),\n                                                                                                            onOpenChange: (open)=>{\n                                                                                                                setIsSelectOpen(open);\n                                                                                                                if (open) {\n                                                                                                                    handleFetchUsers();\n                                                                                                                    setSearchTerm(\"\");\n                                                                                                                }\n                                                                                                            },\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                                                    className: \"w-full h-8 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]\",\n                                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                                                        placeholder: \"Select User\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                        lineNumber: 801,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined)\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                    lineNumber: 800,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                                                    onPointerDown: (e)=>e.stopPropagation(),\n                                                                                                                    onCloseAutoFocus: (e)=>{\n                                                                                                                        e.preventDefault();\n                                                                                                                    },\n                                                                                                                    className: \"bg-white dark:bg-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto mt-1\",\n                                                                                                                    children: [\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                            className: \"sticky top-0 z-10 bg-white dark:bg-gray-700 p-2\",\n                                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                                                ref: inputRef,\n                                                                                                                                type: \"text\",\n                                                                                                                                placeholder: \"Search user...\",\n                                                                                                                                value: searchTerm,\n                                                                                                                                onChange: (e)=>{\n                                                                                                                                    setSearchTerm(e.target.value);\n                                                                                                                                    setSelectedUserIndex(0);\n                                                                                                                                },\n                                                                                                                                onKeyDown: (e)=>{\n                                                                                                                                    const filteredUsers = users.filter((u)=>u.username.toLowerCase().includes(searchTerm.toLowerCase()));\n                                                                                                                                    if (e.key === \"Enter\") {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        e.stopPropagation();\n                                                                                                                                        // Select the currently highlighted user or first filtered user\n                                                                                                                                        if (filteredUsers.length > 0) {\n                                                                                                                                            const userToSelect = filteredUsers[Math.min(selectedUserIndex, filteredUsers.length - 1)];\n                                                                                                                                            handleStageChange(index, stage.id, \"assignedto\", userToSelect.id.toString());\n                                                                                                                                            setSearchTerm(\"\");\n                                                                                                                                            setSelectedUserIndex(0);\n                                                                                                                                            setIsSelectOpen(false);\n                                                                                                                                        }\n                                                                                                                                    } else if (e.key === \"ArrowDown\") {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        e.stopPropagation();\n                                                                                                                                        setSelectedUserIndex((prev)=>Math.min(prev + 1, filteredUsers.length - 1));\n                                                                                                                                    } else if (e.key === \"ArrowUp\") {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        e.stopPropagation();\n                                                                                                                                        setSelectedUserIndex((prev)=>Math.max(prev - 1, 0));\n                                                                                                                                    } else if (e.key === \"Escape\") {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        e.stopPropagation();\n                                                                                                                                        setSearchTerm(\"\");\n                                                                                                                                        setSelectedUserIndex(0);\n                                                                                                                                        setIsSelectOpen(false);\n                                                                                                                                    }\n                                                                                                                                },\n                                                                                                                                onFocus: (e)=>{\n                                                                                                                                    e.stopPropagation();\n                                                                                                                                },\n                                                                                                                                onBlur: (e)=>{\n                                                                                                                                    // Prevent blur when clicking on SelectItem\n                                                                                                                                    const relatedTarget = e.relatedTarget;\n                                                                                                                                    if (relatedTarget && relatedTarget.closest('[role=\"option\"]')) {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        setTimeout(()=>{\n                                                                                                                                            if (inputRef.current) {\n                                                                                                                                                inputRef.current.focus();\n                                                                                                                                            }\n                                                                                                                                        }, 0);\n                                                                                                                                    }\n                                                                                                                                },\n                                                                                                                                className: \"w-full h-8 px-2 rounded border border-gray-300 dark:border-gray-600 text-sm bg-white dark:bg-gray-800 text-black dark:text-white\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                                lineNumber: 813,\n                                                                                                                                columnNumber: 45\n                                                                                                                            }, undefined)\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                            lineNumber: 812,\n                                                                                                                            columnNumber: 43\n                                                                                                                        }, undefined),\n                                                                                                                        areUsersLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                            className: \"flex justify-center items-center p-2 text-gray-500\",\n                                                                                                                            children: \"Loading...\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                            lineNumber: 885,\n                                                                                                                            columnNumber: 45\n                                                                                                                        }, undefined) : users.filter((u)=>u.username.toLowerCase().includes(searchTerm.toLowerCase())).map((u, userIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                                                                value: u.id.toString(),\n                                                                                                                                className: \"px-2 py-1 rounded cursor-pointer transition text-[13px] \".concat(userIndex === selectedUserIndex ? \"bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100\" : \"hover:bg-gray-100 dark:hover:bg-gray-800\"),\n                                                                                                                                onMouseDown: (e)=>{\n                                                                                                                                    e.preventDefault();\n                                                                                                                                },\n                                                                                                                                onMouseEnter: ()=>{\n                                                                                                                                    setSelectedUserIndex(userIndex);\n                                                                                                                                },\n                                                                                                                                onClick: ()=>{\n                                                                                                                                    handleStageChange(index, stage.id, \"assignedto\", u.id.toString());\n                                                                                                                                    setSearchTerm(\"\");\n                                                                                                                                    setSelectedUserIndex(0);\n                                                                                                                                    setIsSelectOpen(false);\n                                                                                                                                },\n                                                                                                                                children: u.username\n                                                                                                                            }, u.id, false, {\n                                                                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                                lineNumber: 898,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, undefined))\n                                                                                                                    ]\n                                                                                                                }, void 0, true, {\n                                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                    lineNumber: 803,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 780,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 776,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                                                            className: \"block text-[11px] text-gray-500 mb-0.5\",\n                                                                                                            children: \"Due Date\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 932,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                            type: \"date\",\n                                                                                                            className: \"w-full h-8 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]\",\n                                                                                                            value: ((_ticket_stages_sidx1 = ticket.stages[sidx]) === null || _ticket_stages_sidx1 === void 0 ? void 0 : (_ticket_stages_sidx_due = _ticket_stages_sidx1.due) === null || _ticket_stages_sidx_due === void 0 ? void 0 : _ticket_stages_sidx_due.split(\"T\")[0]) || \"\",\n                                                                                                            onChange: (e)=>handleStageChange(index, stage.id, \"due\", new Date(e.target.value).toISOString())\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 935,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 931,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 775,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 761,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 760,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            sidx < ticket.pipelineStages.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center mx-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M8 4l8 8-8 8\",\n                                                                                        stroke: \"#9ca3af\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 966,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 960,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 959,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, stage.id, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 759,\n                                                                        columnNumber: 29\n                                                                    }, undefined);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, undefined);\n                            }),\n                            (!selectedRows || selectedRows.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleAddTicketForm,\n                                    variant: \"outline\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 993,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Add Another Ticket\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 988,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 987,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 p-6 rounded-b-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 1004,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Fields marked with * are required\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 1003,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 1009,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        ticketForms.length,\n                                                        \" tickets to create\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 1010,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 1008,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 1002,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: handleClose,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 1016,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleFinalSubmit,\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6\",\n                                            disabled: ticketForms.some((ticket)=>!ticket.pipeline_id || !ticket.owner || !ticket.priority),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 1027,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Create Ticket\",\n                                                ticketForms.length > 1 ? \"s\" : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 1019,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 1015,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 1001,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 1000,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                lineNumber: 472,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n            lineNumber: 471,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n        lineNumber: 470,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTicketModal, \"l1NE/FvsPxnmQE3y/m3uQao98EA=\");\n_c = CreateTicketModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTicketModal);\nvar _c;\n$RefreshReg$(_c, \"CreateTicketModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/ticketing_system/CreateTicket.tsx\n"));

/***/ })

});