"use client";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { CalendarIcon, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { createInvoiceFile } from "../services/invoiceFileService";
import SearchSelect from "@/app/_component/SearchSelect";
import { FormProvider } from "react-hook-form";
import { useInvoiceFilesContext } from "../invoiceFilesContext";

const createInvoiceFileSchema = z.object({
  date: z.date({ required_error: "Date is required" }),
  carrierId: z.number().min(1, "Carrier is required"),
  fileName: z
    .string()
    .min(1, "File name is required")
    .max(255, "File name too long")
    .regex(/^[a-zA-Z0-9._-]+$/, "File name contains invalid characters"),
  noOfPages: z
    .number()
    .min(1, "Must have at least 1 page")
    .max(9999, "Too many pages"),
  assignedTo: z.number().optional(),
});

type CreateInvoiceFileForm = z.infer<typeof createInvoiceFileSchema>;

interface AddInvoiceFileProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userData: any;
  carriers: any[];
  users: any;
}

export function AddInvoiceFile({
  open,
  onOpenChange,
  userData,
  carriers,
  users,
}: AddInvoiceFileProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { triggerReload } = useInvoiceFilesContext();
  const form = useForm<CreateInvoiceFileForm>({
    resolver: zodResolver(createInvoiceFileSchema),
    defaultValues: {
      date: new Date(),
      carrierId: 0,
      fileName: "",
      noOfPages: 1,
      assignedTo: userData?.id ?? undefined,
    },
  });

  const handleOpenChange = (newOpen: boolean) => {
    if (!isSubmitting) {
      onOpenChange(newOpen);
    }
  };

  const carrierOptions = carriers.map((carrier) => ({
    label: carrier.name,
    value: carrier.id.toString(),
  }));

  const userOptions = Array.isArray(users?.data)
    ? users.data.map((user: any) => ({
        label: `${user.firstName} ${user.lastName}`,
        value: user.id.toString(),
      }))
    : [];

  const onSubmit = async (data: CreateInvoiceFileForm) => {
    setIsSubmitting(true);
    try {
      await createInvoiceFile({
        date: format(data.date, "yyyy-MM-dd"),
        carrier: data.carrierId,
        fileName: data.fileName.trim(),
        noOfPages: data.noOfPages,
        assignedTo: data.assignedTo || userData?.id,
        createdBy: userData?.id,
      });

      console.log("Invoice file created successfully");
      toast.success("Invoice file created successfully");
      form.reset();
      triggerReload(); // Trigger reload instead of router.refresh()
      onOpenChange(false);
    } catch (error) {
      toast.error("Failed to create invoice file");
      console.log("Error creating invoice file:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Invoice File</DialogTitle>
        </DialogHeader>
        <FormProvider {...form}>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date *</FormLabel>
                    <FormControl>
                      <DatePicker
                        selected={field.value}
                        onChange={(date: Date | null) => {
                          if (date) {
                            field.onChange(date);
                          }
                        }}
                        placeholderText="Select a date"
                        dateFormat="PPP"
                        className="w-full rounded-md border px-3 py-2 text-sm shadow-sm"
                        wrapperClassName="w-full"
                        autoFocus={false}
                        preventOpenOnFocus={true}
                      />
                    </FormControl>
                    <FormDescription>
                      The date associated with this invoice file.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <SearchSelect
                name="carrierId"
                label="Carrier *"
                placeholder="Select Carrier"
                options={carrierOptions}
                value={
                  form.watch("carrierId")
                    ? form.watch("carrierId").toString()
                    : ""
                }
                onValueChange={(val) => {
                  if (val)
                    form.setValue("carrierId", parseInt(val), {
                      shouldValidate: true,
                      shouldDirty: true,
                    });
                  else
                    form.setValue("carrierId", 0, {
                      shouldValidate: true,
                      shouldDirty: true,
                    });
                }}
                form={form}
                dropdownMaxHeight="max-h-40"
              />

              <FormField
                control={form.control}
                name="fileName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>File Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="invoice_2024_001.pdf" {...field} />
                    </FormControl>
                    <FormDescription>
                      Enter the file name (alphanumeric, dots, dashes,
                      underscores only).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="noOfPages"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Number of Pages *</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max="9999"
                        placeholder="1"
                        {...field}
                        onChange={(e) =>
                          field.onChange(Number.parseInt(e.target.value) || 1)
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      Total number of pages in the invoice file.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <SearchSelect
                name="assignedTo"
                label="Assign To"
                placeholder="Select Assign To"
                options={userOptions}
                value={
                  form.watch("assignedTo") !== undefined
                    ? form.watch("assignedTo").toString()
                    : userData?.id
                    ? userData.id.toString()
                    : ""
                }
                onValueChange={(val) => {
                  if (val)
                    form.setValue("assignedTo", parseInt(val), {
                      shouldValidate: true,
                      shouldDirty: true,
                    });
                  else
                    form.setValue("assignedTo", undefined, {
                      shouldValidate: true,
                      shouldDirty: true,
                    });
                }}
                form={form}
                dropdownMaxHeight="max-h-20"
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    form.reset();
                    handleOpenChange(false);
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Create File
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
}
