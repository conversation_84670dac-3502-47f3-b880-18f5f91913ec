// components/tracksheet/LegrandSection.tsx
"use client";
import React, { useEffect } from "react";
import LegrandDetailsComponent from "./LegrandDetailsComponent";
import { useWarningValidation } from "../hooks/useWarningValidation";
import WarningDisplay from "./WarningDisplay";

interface LegrandSectionProps {
  entryIndex: number;
  clientOptions: any[];
  legrandData: any[];
  form: any;
  handleLegrandDataChange: (
    entryIndex: number,
    businessUnit: string,
    divisionCode: string
  ) => void;
}

// Utility to render the DC/CV toggle buttons
const renderToggle = (
  type: "shipperType" | "consigneeType" | "billtoType",
  index: number,
  form: any
) => (
  <div className="flex gap-1 mb-1">
    {["DC", "CV"].map((value) => (
      <button
        key={value}
        type="button"
        className={`px-2 py-0.5 rounded-full border text-xs font-semibold transition-all duration-150
          ${form.getValues(`entries.${index}.${type}`) === value
            ? "bg-blue-600 text-white border-blue-600"
            : "bg-white text-gray-700 border-gray-300 hover:bg-gray-100"}
        `}
        onClick={() =>
          form.setValue(`entries.${index}.${type}`, value, {
            shouldValidate: true,
          })
        }
      >
        {value}
      </button>
    ))}
  </div>
);


const LegrandSection: React.FC<LegrandSectionProps> = ({
  entryIndex,
  clientOptions,
  legrandData,
  form,
  handleLegrandDataChange,
}) => {
  console.log(`[LegrandSection] Rendering for entry index: ${entryIndex}`); // Step 1: Confirm render
  const { warnings, validateWarnings } = useWarningValidation();

  const watchedValues = form.watch([
    `entries.${entryIndex}.legrandFreightTerms`,
    `entries.${entryIndex}.shipperType`,
    `entries.${entryIndex}.consigneeType`,
    `entries.${entryIndex}.billtoType`,
  ]);

  useEffect(() => {
    const [
      legrandFreightTerms,
      shipperType,
      consigneeType,
      billtoType,
    ] = watchedValues;

    // Map frontend values to API values
    const freightTermMap: { [key: string]: string } = {
        "Prepaid": "PREPAID",
        "Collect": "COLLECT",
        "Third Party Billing": "THIRD_PARTY",
    };

    const apiFreightTerm = freightTermMap[legrandFreightTerms] || "";

    validateWarnings({
      freightTerm: apiFreightTerm,
      shipperAddressType: shipperType,
      consigneeAddressType: consigneeType,
      billToAddressType: billtoType,
    });
  }, [watchedValues, validateWarnings]);
  
  const formValues = form.getValues();
  const entry = formValues.entries?.[entryIndex] as any;
  const entryClientId = entry?.clientId || formValues.clientId || "";
  const entryClientName =
    clientOptions?.find((c: any) => c.value === entryClientId)?.name || "";
    
  // Step 2: Log the client name to see what it is
  console.log(`[LegrandSection] Client name for index ${entryIndex}: '${entryClientName}'`);

  // Helper to filter warnings for a specific party based on keywords in the message
  const getWarningsForParty = (party: 'Shipper' | 'Consignee' | 'Bill-to') => {
    if (!warnings || !warnings.success) {
      return { HIGH: [], MEDIUM: [] };
    }

    const filter = (arr: any[]) => arr.filter(w => w.message.includes(party));

    return {
      HIGH: filter(warnings.warnings.HIGH),
      MEDIUM: filter(warnings.warnings.MEDIUM),
    };
  };

  const shipperWarnings = getWarningsForParty('Shipper');
  const consigneeWarnings = getWarningsForParty('Consignee');
  const billtoWarnings = getWarningsForParty('Bill-to');

  // Step 3: Make the client name check case-insensitive and trim whitespace
  if (entryClientName?.trim().toUpperCase() !== "LEGRAND") {
    console.log(`[LegrandSection] Skipping render because client name is not 'LEGRAND'.`);
    return null;
  }

  const selectedFreightTerm = form.getValues(`entries.${entryIndex}.legrandFreightTerms`);

  return (
    <div className="mb-3">
      {/* Error display if present */}
      {form.formState.errors?.entries?.[entryIndex]?.legrandFreightTerms && (
        <div className="text-xs text-red-600 mb-2">
          {form.formState.errors.entries[entryIndex].legrandFreightTerms.message}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3">
        {/* Section: Shipper */}
        <div>
            <LegrandDetailsComponent
                form={form}
                entryIndex={entryIndex}
                blockTitle="Shipper"
                fieldPrefix="shipper"
                legrandData={legrandData}
                onLegrandDataChange={handleLegrandDataChange}
                isCV={form.getValues(`entries.${entryIndex}.shipperType`) === "CV"}
                dcCvToggle={renderToggle("shipperType", entryIndex, form)}
                selectedFreightTerm={selectedFreightTerm}
                blockType="Shipper"
                highlight={selectedFreightTerm === "Prepaid"}
            />
            <WarningDisplay warnings={shipperWarnings.HIGH} severity="HIGH" />
            <WarningDisplay warnings={shipperWarnings.MEDIUM} severity="MEDIUM" />
        </div>

        {/* Section: Consignee */}
        <div>
            <LegrandDetailsComponent
                form={form}
                entryIndex={entryIndex}
                blockTitle="Consignee"
                fieldPrefix="consignee"
                legrandData={legrandData}
                disabled={entryClientName?.trim().toUpperCase() !== "LEGRAND"}
                onLegrandDataChange={handleLegrandDataChange}
                isCV={form.getValues(`entries.${entryIndex}.consigneeType`) === "CV"}
                dcCvToggle={renderToggle("consigneeType", entryIndex, form)}
                selectedFreightTerm={selectedFreightTerm}
                blockType="Consignee"
                highlight={selectedFreightTerm === "Collect"}
            />
            <WarningDisplay warnings={consigneeWarnings.HIGH} severity="HIGH" />
            <WarningDisplay warnings={consigneeWarnings.MEDIUM} severity="MEDIUM" />
        </div>

        {/* Section: Bill-To */}
        <div>
            <LegrandDetailsComponent
                form={form}
                entryIndex={entryIndex}
                blockTitle="Bill-to"
                fieldPrefix="billto"
                legrandData={legrandData}
                onLegrandDataChange={handleLegrandDataChange}
                isCV={form.getValues(`entries.${entryIndex}.billtoType`) === "CV"}
                dcCvToggle={renderToggle("billtoType", entryIndex, form)}
                selectedFreightTerm={selectedFreightTerm}
                blockType="Bill-to"
                highlight={selectedFreightTerm === "Third Party Billing"}
            />
            <WarningDisplay warnings={billtoWarnings.HIGH} severity="HIGH" />
            <WarningDisplay warnings={billtoWarnings.MEDIUM} severity="MEDIUM" />
        </div>
      </div>
    </div>
  );
};

export default LegrandSection;

