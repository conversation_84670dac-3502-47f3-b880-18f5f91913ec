"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/ticketing_system/CreateTicket.tsx":
/*!****************************************************************!*\
  !*** ./app/user/trackSheets/ticketing_system/CreateTicket.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Info,Plus,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_stageColorUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/stageColorUtils */ \"(app-pages-browser)/./lib/stageColorUtils.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PRIORITY_OPTIONS = [\n    {\n        value: \"High\",\n        label: \"High\",\n        color: \"bg-red-100 text-red-800 border-red-200\",\n        icon: \"\\uD83D\\uDD34\"\n    },\n    {\n        value: \"Medium\",\n        label: \"Medium\",\n        color: \"bg-yellow-100 text-yellow-800 border-yellow-200\",\n        icon: \"\\uD83D\\uDFE1\"\n    },\n    {\n        value: \"Low\",\n        label: \"Low\",\n        color: \"bg-green-100 text-green-800 border-green-200\",\n        icon: \"\\uD83D\\uDFE2\"\n    }\n];\nconst CreateTicketModal = (param)=>{\n    let { isOpen, onClose, onClearSelection, selectedRows = [] } = param;\n    _s();\n    const [ticketForms, setTicketForms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pipelines, setPipelines] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [creator, setCreator] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const pipelineSelectRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dataFetched = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [arePipelinesLoading, setArePipelinesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [areUsersLoading, setAreUsersLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBulkFill, setIsBulkFill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTicketIndex, setActiveTicketIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [ownerAutofillError, setOwnerAutofillError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSelectOpen, setIsSelectOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUserIndex, setSelectedUserIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isSelectOpen) {\n            const timer = setTimeout(()=>{\n                if (inputRef.current && typeof inputRef.current.focus === \"function\") {\n                    inputRef.current.focus();\n                    // Ensure the input stays focused\n                    inputRef.current.addEventListener(\"blur\", (e)=>{\n                        const relatedTarget = e.relatedTarget;\n                        if (relatedTarget && !relatedTarget.closest('[role=\"option\"]')) {\n                            setTimeout(()=>{\n                                if (inputRef.current && isSelectOpen) {\n                                    inputRef.current.focus();\n                                }\n                            }, 0);\n                        }\n                    }, {\n                        once: true\n                    });\n                }\n            }, 50);\n            return ()=>clearTimeout(timer);\n        }\n    }, [\n        isSelectOpen\n    ]);\n    const getFormCompletion = (ticket)=>{\n        var _ticket_stages;\n        const requiredFields = [\n            \"pipeline_id\",\n            \"owner\",\n            \"priority\"\n        ];\n        const completedFields = requiredFields.filter((field)=>ticket[field]);\n        const stageCompletion = ((_ticket_stages = ticket.stages) === null || _ticket_stages === void 0 ? void 0 : _ticket_stages.length) > 0 ? ticket.stages.filter((stage)=>stage.assignedto && stage.due).length / ticket.stages.length : 0;\n        return Math.round((completedFields.length / requiredFields.length * 0.6 + stageCompletion * 0.4) * 100);\n    };\n    const validateForm = (ticket)=>{\n        const errors = {};\n        if (!ticket.pipeline_id) errors.pipeline = \"Pipeline is required\";\n        if (!ticket.owner) errors.owner = \"Owner is required\";\n        if (!ticket.priority) errors.priority = \"Priority is required\";\n        return errors;\n    };\n    const { setCustomFieldsReloadTrigger } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_16__.TrackSheetContext);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isOpen) {\n            dataFetched.current = false;\n            setUsers([]);\n            setPipelines([]);\n            setCreator(null);\n            setValidationErrors({});\n            setActiveTicketIndex(0);\n            return;\n        }\n        if (dataFetched.current) {\n            return;\n        }\n        dataFetched.current = true;\n        const fetchInitialData = async ()=>{\n            try {\n                const currentUserInfo = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GETCURRENT_USER);\n                setCreator(currentUserInfo || null);\n                // Fetch CSA and set owner here\n                if (currentUserInfo && currentUserInfo.id) {\n                    try {\n                        const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GET_CSA(currentUserInfo.id), {\n                            credentials: \"include\"\n                        });\n                        if (!res.ok) throw new Error(\"Failed to fetch CSA\");\n                        const data = await res.json();\n                        const csa = data.csa;\n                        const ownerName = (csa === null || csa === void 0 ? void 0 : csa.name) || (csa === null || csa === void 0 ? void 0 : csa.username) || \"\";\n                        if (!ownerName) throw new Error(\"No CSA found for this user\");\n                        setTicketForms((prev)=>prev.map((form)=>({\n                                    ...form,\n                                    owner: ownerName\n                                })));\n                        setOwnerAutofillError(false);\n                    } catch (err) {\n                        setOwnerAutofillError(true);\n                        sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Could not auto-fill owner (CSA): \" + ((err === null || err === void 0 ? void 0 : err.message) || \"Unknown error\"));\n                    }\n                }\n            } catch (err) {\n                dataFetched.current = false;\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load user data.\");\n            }\n        };\n        fetchInitialData();\n    }, [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            const initialForms = selectedRows && selectedRows.length > 0 ? selectedRows.map((row)=>({\n                    tracksheetid: row.id || \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    description: \"\",\n                    stages: [],\n                    pipelineStages: []\n                })) : [\n                {\n                    tracksheetid: \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    description: \"\",\n                    stages: [],\n                    pipelineStages: []\n                }\n            ];\n            setTicketForms(initialForms);\n            requestAnimationFrame(()=>{\n                setTimeout(()=>{\n                    var _pipelineSelectRef_current;\n                    (_pipelineSelectRef_current = pipelineSelectRef.current) === null || _pipelineSelectRef_current === void 0 ? void 0 : _pipelineSelectRef_current.focus();\n                }, 30);\n            });\n        }\n    }, [\n        isOpen,\n        selectedRows\n    ]);\n    const handleFetchPipelines = async ()=>{\n        if (pipelines.length > 0 || arePipelinesLoading) return;\n        setArePipelinesLoading(true);\n        try {\n            const pipelinesData = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.pipeline_routes.GET_PIPELINE);\n            setPipelines(pipelinesData.data || []);\n        } catch (err) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load pipelines.\");\n        } finally{\n            setArePipelinesLoading(false);\n        }\n    };\n    const handleFetchUsers = async ()=>{\n        if (users.length > 0 || areUsersLoading) return;\n        setAreUsersLoading(true);\n        try {\n            const usersData = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.employee_routes.GETALL_USERS);\n            setUsers(usersData.data || []);\n        } catch (err) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to load users.\");\n        } finally{\n            setAreUsersLoading(false);\n        }\n    };\n    const handleClose = ()=>{\n        var _document_activeElement;\n        (_document_activeElement = document.activeElement) === null || _document_activeElement === void 0 ? void 0 : _document_activeElement.blur();\n        onClose();\n        onClearSelection();\n    };\n    const handleFieldChange = (index, field, value)=>{\n        setTicketForms((prev)=>{\n            const shouldApplyToAll = isBulkFill && prev.length > 1;\n            return prev.map((t, i)=>{\n                if (shouldApplyToAll || i === index) {\n                    if (field === \"pipeline_id\") {\n                        const selectedPipeline = pipelines.find((p)=>p.id === value);\n                        const stages = (selectedPipeline === null || selectedPipeline === void 0 ? void 0 : selectedPipeline.stages) || [];\n                        return {\n                            ...t,\n                            pipeline_id: value,\n                            pipelineStages: stages,\n                            stages: stages.map((s)=>({\n                                    stageid: s.id,\n                                    assignedto: \"\",\n                                    due: new Date().toISOString()\n                                }))\n                        };\n                    }\n                    return {\n                        ...t,\n                        [field]: value\n                    };\n                }\n                return t;\n            });\n        });\n        setValidationErrors((prev)=>{\n            const newErrors = {\n                ...prev\n            };\n            delete newErrors[\"\".concat(field, \"_\").concat(index)];\n            return newErrors;\n        });\n    };\n    const handleStageChange = (index, stageid, field, value)=>{\n        setTicketForms((prev)=>{\n            const shouldApplyToAll = isBulkFill && prev.length > 1;\n            return prev.map((t, i)=>{\n                if (shouldApplyToAll || i === index) {\n                    const updatedStages = t.stages.map((s)=>s.stageid === stageid ? {\n                            ...s,\n                            [field]: value\n                        } : s);\n                    return {\n                        ...t,\n                        stages: updatedStages\n                    };\n                }\n                return t;\n            });\n        });\n        setValidationErrors((prev)=>{\n            var _ticketForms_index_stages, _ticketForms_index;\n            const newErrors = {\n                ...prev\n            };\n            const stageIndex = (_ticketForms_index = ticketForms[index]) === null || _ticketForms_index === void 0 ? void 0 : (_ticketForms_index_stages = _ticketForms_index.stages) === null || _ticketForms_index_stages === void 0 ? void 0 : _ticketForms_index_stages.findIndex((s)=>s.stageid === stageid);\n            if (stageIndex !== -1) {\n                delete newErrors[\"stage_\".concat(stageIndex, \"_\").concat(field)];\n            }\n            return newErrors;\n        });\n    };\n    const handleAddTicketForm = ()=>{\n        setTicketForms((prev)=>[\n                ...prev,\n                {\n                    tracksheetid: \"\",\n                    description: \"\",\n                    pipeline_id: \"\",\n                    owner: \"\",\n                    priority: \"\",\n                    stages: [],\n                    pipelineStages: []\n                }\n            ]);\n    };\n    const handleRemoveTicketForm = (index)=>{\n        setTicketForms((prev)=>prev.filter((_, i)=>i !== index));\n        if (activeTicketIndex >= index && activeTicketIndex > 0) {\n            setActiveTicketIndex(activeTicketIndex - 1);\n        }\n    };\n    const handleFinalSubmit = async ()=>{\n        const allErrors = {};\n        ticketForms.forEach((ticket, index)=>{\n            const errors = validateForm(ticket);\n            Object.keys(errors).forEach((key)=>{\n                allErrors[\"\".concat(key, \"_\").concat(index)] = errors[key];\n            });\n        });\n        if (Object.keys(allErrors).length > 0) {\n            setValidationErrors(allErrors);\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Please fix the validation errors before submitting.\");\n            return;\n        }\n        const validTickets = ticketForms.filter((t)=>t.pipeline_id && t.owner && t.priority && t.stages.length);\n        if (!validTickets.length) return sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Fill all required fields.\");\n        const ticketsWithCreator = validTickets.map((ticket, index)=>{\n            var _selectedRows_index;\n            return {\n                tracksheetid: ticket.tracksheetid,\n                title: \"Invoice \".concat(((_selectedRows_index = selectedRows[index]) === null || _selectedRows_index === void 0 ? void 0 : _selectedRows_index.invoice) || ticket.tracksheetid || \"N/A\"),\n                description: ticket.description || \"\",\n                pipeline_id: ticket.pipeline_id,\n                owner: ticket.owner,\n                priority: ticket.priority,\n                stages: ticket.stages.map((s)=>({\n                        stageid: s.stageid,\n                        assignedto: s.assignedto,\n                        due: s.due\n                    })),\n                createdBy: creator === null || creator === void 0 ? void 0 : creator.username\n            };\n        });\n        try {\n            const data = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_6__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_7__.ticket_routes.CREATE_TICKET, \"POST\", ticketsWithCreator);\n            if (data && data.message && data.message.toLowerCase().includes(\"success\")) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Tickets created successfully.\");\n                setCustomFieldsReloadTrigger((prev)=>prev + 1);\n                handleClose();\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(data && data.message || \"Failed to create.\");\n            }\n        } catch (e) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Network/server error.\");\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_15__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-900 rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] p-0 relative transition-transform scale-100 border border-gray-200 dark:border-gray-800 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 p-6 rounded-t-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold mb-2 flex items-center gap-2 text-gray-900 dark:text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Create New Ticket\",\n                                            ticketForms.length > 1 ? \"s\" : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 text-sm\",\n                                        children: ticketForms.length > 1 ? \"Creating \".concat(ticketForms.length, \" tickets from selected items\") : \"Fill in the details below to create a new ticket\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 11\n                    }, undefined),\n                    ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-800 px-4 py-2 border-b border-gray-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1 overflow-x-auto\",\n                            children: ticketForms.map((ticket, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTicketIndex(index),\n                                    className: \"flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium transition-all whitespace-nowrap \".concat(activeTicketIndex === index ? \"bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white shadow\" : \"bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Ticket \",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: getFormCompletion(ticket) === 100 ? \"default\" : \"secondary\",\n                                            className: \"text-xs px-2 py-0.5\",\n                                            children: [\n                                                getFormCompletion(ticket),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-grow overflow-y-auto p-6\",\n                        children: [\n                            ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                className: \"mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                    className: \"p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                htmlFor: \"bulk-fill-switch\",\n                                                                className: \"text-[15px] font-medium\",\n                                                                children: \"Apply changes to all tickets\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 leading-tight\",\n                                                                children: \"When enabled, changes will be applied to all tickets simultaneously\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_11__.Switch, {\n                                                id: \"bulk-fill-switch\",\n                                                checked: isBulkFill,\n                                                onCheckedChange: setIsBulkFill,\n                                                className: \"scale-90\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 15\n                            }, undefined),\n                            ticketForms.map((ticket, index)=>{\n                                var _selectedRows_index, _ticket_pipelineStages, _pipelines_find;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\".concat(ticketForms.length > 1 && activeTicketIndex !== index ? \"hidden\" : \"\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardHeader, {\n                                                className: \"pb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 dark:text-blue-400 font-semibold\",\n                                                                        children: index + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardTitle, {\n                                                                            className: \"text-lg\",\n                                                                            children: [\n                                                                                \"Invoice\",\n                                                                                \" \",\n                                                                                ((_selectedRows_index = selectedRows[index]) === null || _selectedRows_index === void 0 ? void 0 : _selectedRows_index.invoice) || ticket.tracksheetid || \"N/A\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_12__.Progress, {\n                                                                                    value: getFormCompletion(ticket),\n                                                                                    className: \"w-32 h-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 576,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-500\",\n                                                                                    children: [\n                                                                                        getFormCompletion(ticket),\n                                                                                        \"% complete\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 580,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleRemoveTicketForm(index),\n                                                            className: \"text-gray-400 hover:text-red-500 transition-colors p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-5 h-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 602,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Owner \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 603,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                        value: ticket.owner,\n                                                                        onChange: (e)=>handleFieldChange(index, \"owner\", e.target.value),\n                                                                        readOnly: !ownerAutofillError,\n                                                                        className: \"bg-gray-50 dark:bg-gray-800\",\n                                                                        placeholder: ownerAutofillError ? \"Enter owner manually\" : \"Auto-filled from CSA\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 605,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    ownerAutofillError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs mt-1\",\n                                                                        children: \"Could not auto-fill Owner. Please enter manually.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 619,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    validationErrors[\"owner_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 625,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"owner_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 633,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Pipeline \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 36\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                        value: ticket.pipeline_id,\n                                                                        onValueChange: (value)=>handleFieldChange(index, \"pipeline_id\", value),\n                                                                        onOpenChange: (open)=>{\n                                                                            if (open) handleFetchPipelines();\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                ref: index === 0 ? pipelineSelectRef : null,\n                                                                                className: \"\".concat(validationErrors[\"pipeline_\".concat(index)] ? \"border-red-500 focus:ring-red-500\" : \"\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                    placeholder: \"Select Pipeline\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 653,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 645,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                children: arePipelinesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-center items-center p-4 text-gray-500\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 658,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 657,\n                                                                                    columnNumber: 31\n                                                                                }, undefined) : pipelines.map((p)=>{\n                                                                                    var _p_stages;\n                                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                        value: p.id,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: p.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 664,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    variant: \"outline\",\n                                                                                                    className: \"text-xs\",\n                                                                                                    children: [\n                                                                                                        ((_p_stages = p.stages) === null || _p_stages === void 0 ? void 0 : _p_stages.length) || 0,\n                                                                                                        \" stages\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 665,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 663,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, p.id, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 662,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined);\n                                                                                })\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 655,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 636,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    validationErrors[\"pipeline_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 679,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"pipeline_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 678,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 687,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Priority \",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-red-500\",\n                                                                                children: \"*\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 688,\n                                                                                columnNumber: 36\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 686,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                        value: ticket.priority,\n                                                                        onValueChange: (value)=>handleFieldChange(index, \"priority\", value),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                className: \"\".concat(validationErrors[\"priority_\".concat(index)] ? \"border-red-500 focus:ring-red-500\" : \"\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                    placeholder: \"Select Priority\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 703,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 696,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                children: PRIORITY_OPTIONS.map((opt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                        value: opt.value,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    children: opt.icon\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 709,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    className: opt.color,\n                                                                                                    children: opt.label\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 710,\n                                                                                                    columnNumber: 35\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 708,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, opt.value, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 707,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 705,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    validationErrors[\"priority_\".concat(index)] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-500 text-xs flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 720,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            validationErrors[\"priority_\".concat(index)]\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                        className: \"text-sm font-medium flex items-center gap-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 728,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            \"Description\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 727,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_14__.Textarea, {\n                                                                        value: ticket.description,\n                                                                        onChange: (e)=>handleFieldChange(index, \"description\", e.target.value),\n                                                                        placeholder: \"Enter ticket description...\",\n                                                                        className: \"h-10 resize-none\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 731,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 726,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    ((_ticket_pipelineStages = ticket.pipelineStages) === null || _ticket_pipelineStages === void 0 ? void 0 : _ticket_pipelineStages.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-lg font-semibold\",\n                                                                        children: ((_pipelines_find = pipelines.find((p)=>p.id === ticket.pipeline_id)) === null || _pipelines_find === void 0 ? void 0 : _pipelines_find.name) || \"Pipeline\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 749,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-sm\",\n                                                                        children: [\n                                                                            ticket.pipelineStages.length,\n                                                                            \" stages\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 753,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-row gap-4 overflow-x-auto flex-nowrap py-4\",\n                                                                children: ticket.pipelineStages.map((stage, sidx)=>{\n                                                                    var _ticket_stages_sidx, _ticket_stages_sidx_due, _ticket_stages_sidx1;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.Card, {\n                                                                                className: \"min-w-[220px] max-w-[260px] flex-shrink-0 shadow-sm border border-gray-200 dark:border-gray-700\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_13__.CardContent, {\n                                                                                    className: \"p-4 flex flex-col gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2 mb-1\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                                                    className: (0,_lib_stageColorUtils__WEBPACK_IMPORTED_MODULE_9__.getStageColor)(stage.id),\n                                                                                                    children: stage.name\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 763,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"text-xs text-gray-400\",\n                                                                                                    children: [\n                                                                                                        \"#\",\n                                                                                                        sidx + 1\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 766,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 762,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        stage.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"text-xs text-gray-500 mb-1\",\n                                                                                            children: stage.description\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 771,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"space-y-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                                                            className: \"block text-[11px] text-gray-500 mb-0.5\",\n                                                                                                            children: \"Assign To\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 777,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                                                            value: ((_ticket_stages_sidx = ticket.stages[sidx]) === null || _ticket_stages_sidx === void 0 ? void 0 : _ticket_stages_sidx.assignedto) || \"\",\n                                                                                                            onValueChange: (value)=>handleStageChange(index, stage.id, \"assignedto\", value),\n                                                                                                            onOpenChange: (open)=>{\n                                                                                                                setIsSelectOpen(open);\n                                                                                                                if (open) {\n                                                                                                                    handleFetchUsers();\n                                                                                                                    setSearchTerm(\"\");\n                                                                                                                }\n                                                                                                            },\n                                                                                                            children: [\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                                                                    className: \"w-full h-8 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]\",\n                                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                                                        placeholder: \"Select User\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                        lineNumber: 801,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined)\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                    lineNumber: 800,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined),\n                                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                                                                    onPointerDown: (e)=>e.stopPropagation(),\n                                                                                                                    onCloseAutoFocus: (e)=>{\n                                                                                                                        e.preventDefault();\n                                                                                                                    },\n                                                                                                                    className: \"bg-white dark:bg-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto mt-1\",\n                                                                                                                    children: [\n                                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                            className: \"sticky top-0 z-10 bg-white dark:bg-gray-700 p-2\",\n                                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                                                ref: inputRef,\n                                                                                                                                type: \"text\",\n                                                                                                                                placeholder: \"Search user...\",\n                                                                                                                                value: searchTerm,\n                                                                                                                                onChange: (e)=>{\n                                                                                                                                    setSearchTerm(e.target.value);\n                                                                                                                                    setSelectedUserIndex(0);\n                                                                                                                                },\n                                                                                                                                onKeyDown: (e)=>{\n                                                                                                                                    const filteredUsers = users.filter((u)=>u.username.toLowerCase().includes(searchTerm.toLowerCase()));\n                                                                                                                                    if (e.key === \"Enter\") {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        e.stopPropagation();\n                                                                                                                                        // Select the currently highlighted user or first filtered user\n                                                                                                                                        if (filteredUsers.length > 0) {\n                                                                                                                                            const userToSelect = filteredUsers[Math.min(selectedUserIndex, filteredUsers.length - 1)];\n                                                                                                                                            handleStageChange(index, stage.id, \"assignedto\", userToSelect.id.toString());\n                                                                                                                                            setSearchTerm(\"\");\n                                                                                                                                            setSelectedUserIndex(0);\n                                                                                                                                            setIsSelectOpen(false);\n                                                                                                                                        }\n                                                                                                                                    } else if (e.key === \"ArrowDown\") {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        e.stopPropagation();\n                                                                                                                                        setSelectedUserIndex((prev)=>Math.min(prev + 1, filteredUsers.length - 1));\n                                                                                                                                    } else if (e.key === \"ArrowUp\") {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        e.stopPropagation();\n                                                                                                                                        setSelectedUserIndex((prev)=>Math.max(prev - 1, 0));\n                                                                                                                                    } else if (e.key === \"Escape\") {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        e.stopPropagation();\n                                                                                                                                        setSearchTerm(\"\");\n                                                                                                                                        setSelectedUserIndex(0);\n                                                                                                                                        setIsSelectOpen(false);\n                                                                                                                                    }\n                                                                                                                                },\n                                                                                                                                onFocus: (e)=>{\n                                                                                                                                    e.stopPropagation();\n                                                                                                                                },\n                                                                                                                                onBlur: (e)=>{\n                                                                                                                                    // Prevent blur when clicking on SelectItem\n                                                                                                                                    const relatedTarget = e.relatedTarget;\n                                                                                                                                    if (relatedTarget && relatedTarget.closest('[role=\"option\"]')) {\n                                                                                                                                        e.preventDefault();\n                                                                                                                                        setTimeout(()=>{\n                                                                                                                                            if (inputRef.current) {\n                                                                                                                                                inputRef.current.focus();\n                                                                                                                                            }\n                                                                                                                                        }, 0);\n                                                                                                                                    }\n                                                                                                                                },\n                                                                                                                                className: \"w-full h-8 px-2 rounded border border-gray-300 dark:border-gray-600 text-sm bg-white dark:bg-gray-800 text-black dark:text-white\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                                lineNumber: 813,\n                                                                                                                                columnNumber: 45\n                                                                                                                            }, undefined)\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                            lineNumber: 812,\n                                                                                                                            columnNumber: 43\n                                                                                                                        }, undefined),\n                                                                                                                        areUsersLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                            className: \"flex justify-center items-center p-2 text-gray-500\",\n                                                                                                                            children: \"Loading...\"\n                                                                                                                        }, void 0, false, {\n                                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                            lineNumber: 885,\n                                                                                                                            columnNumber: 45\n                                                                                                                        }, undefined) : users.filter((u)=>u.username.toLowerCase().includes(searchTerm.toLowerCase())).map((u)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                                                                value: u.id.toString(),\n                                                                                                                                className: \"px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded cursor-pointer transition text-[13px]\",\n                                                                                                                                onMouseDown: (e)=>{\n                                                                                                                                    e.preventDefault();\n                                                                                                                                },\n                                                                                                                                onClick: ()=>{\n                                                                                                                                    handleStageChange(index, stage.id, \"assignedto\", u.id.toString());\n                                                                                                                                    setSearchTerm(\"\");\n                                                                                                                                    setIsSelectOpen(false);\n                                                                                                                                },\n                                                                                                                                children: u.username\n                                                                                                                            }, u.id, false, {\n                                                                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                                lineNumber: 898,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, undefined))\n                                                                                                                    ]\n                                                                                                                }, void 0, true, {\n                                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                                    lineNumber: 803,\n                                                                                                                    columnNumber: 41\n                                                                                                                }, undefined)\n                                                                                                            ]\n                                                                                                        }, void 0, true, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 780,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 776,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_10__.Label, {\n                                                                                                            className: \"block text-[11px] text-gray-500 mb-0.5\",\n                                                                                                            children: \"Due Date\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 924,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                                            type: \"date\",\n                                                                                                            className: \"w-full h-8 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]\",\n                                                                                                            value: ((_ticket_stages_sidx1 = ticket.stages[sidx]) === null || _ticket_stages_sidx1 === void 0 ? void 0 : (_ticket_stages_sidx_due = _ticket_stages_sidx1.due) === null || _ticket_stages_sidx_due === void 0 ? void 0 : _ticket_stages_sidx_due.split(\"T\")[0]) || \"\",\n                                                                                                            onChange: (e)=>handleStageChange(index, stage.id, \"due\", new Date(e.target.value).toISOString())\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                            lineNumber: 927,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                                    lineNumber: 923,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                            lineNumber: 775,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 761,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 760,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            sidx < ticket.pipelineStages.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center mx-1\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    width: \"20\",\n                                                                                    height: \"20\",\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        d: \"M8 4l8 8-8 8\",\n                                                                                        stroke: \"#9ca3af\",\n                                                                                        strokeWidth: \"2\",\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                        lineNumber: 958,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                    lineNumber: 952,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                                lineNumber: 951,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, stage.id, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                        lineNumber: 759,\n                                                                        columnNumber: 29\n                                                                    }, undefined);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, undefined);\n                            }),\n                            (!selectedRows || selectedRows.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleAddTicketForm,\n                                    variant: \"outline\",\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Add Another Ticket\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 980,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                lineNumber: 979,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 p-6 rounded-b-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 996,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Fields marked with * are required\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 997,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        ticketForms.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 1001,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        ticketForms.length,\n                                                        \" tickets to create\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 1000,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            onClick: handleClose,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 1008,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: handleFinalSubmit,\n                                            className: \"bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6\",\n                                            disabled: ticketForms.some((ticket)=>!ticket.pipeline_id || !ticket.owner || !ticket.priority),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Info_Plus_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                                    lineNumber: 1019,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Create Ticket\",\n                                                ticketForms.length > 1 ? \"s\" : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                                    lineNumber: 1007,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                            lineNumber: 993,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                        lineNumber: 992,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n                lineNumber: 472,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n            lineNumber: 471,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ticketing_system\\\\CreateTicket.tsx\",\n        lineNumber: 470,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTicketModal, \"l1NE/FvsPxnmQE3y/m3uQao98EA=\");\n_c = CreateTicketModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTicketModal);\nvar _c;\n$RefreshReg$(_c, \"CreateTicketModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/ticketing_system/CreateTicket.tsx\n"));

/***/ })

});