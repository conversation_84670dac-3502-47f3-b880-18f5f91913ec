import { createContext, useContext, useState, ReactNode } from "react";

interface InvoiceFilesContext {
  reloadTrigger: number;
  setReloadTrigger: React.Dispatch<React.SetStateAction<number>>;
  triggerReload: () => void;
}

export const InvoiceFilesContext = createContext<InvoiceFilesContext>({
  reloadTrigger: 0,
  setReloadTrigger: () => {},
  triggerReload: () => {},
});

// Provider component
interface InvoiceFilesProviderProps {
  children: ReactNode;
}

export const InvoiceFilesProvider = ({ children }: InvoiceFilesProviderProps) => {
  const [reloadTrigger, setReloadTrigger] = useState(0);

  const triggerReload = () => {
    setReloadTrigger(prev => prev + 1);
  };

  return (
    <InvoiceFilesContext.Provider
      value={{
        reloadTrigger,
        setReloadTrigger,
        triggerReload,
      }}
    >
      {children}
    </InvoiceFilesContext.Provider>
  );
};

// Custom hook to use the context
export const useInvoiceFilesContext = (): InvoiceFilesContext => {
  const context = useContext(InvoiceFilesContext);
  if (!context) {
    throw new Error("useInvoiceFilesContext must be used within an InvoiceFilesProvider");
  }
  return context;
};
